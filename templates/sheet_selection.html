{% extends "base.html" %}

{% block title %}选择工作表 - 摩托车模拟登记表管理系统{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- 页面标题 -->
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h2 class="card-title mb-2">
                        <i class="bi bi-file-earmark-excel"></i>
                        选择要导入的工作表
                    </h2>
                    <p class="card-text mb-0">
                        文件：<strong>{{ original_filename }}</strong> 
                        <span class="badge bg-light text-dark ms-2">共 {{ worksheets|length }} 个工作表</span>
                    </p>
                </div>
            </div>

            <!-- 工作表选择表单 -->
            <form action="{{ url_for('process_sheets') }}" method="post" id="sheetSelectionForm">
                <input type="hidden" name="filename" value="{{ filename }}">
                <input type="hidden" name="original_filename" value="{{ original_filename }}">
                
                <!-- 操作按钮区域 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="selectAll()">
                                        <i class="bi bi-check-all"></i> 全选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="selectNone()">
                                        <i class="bi bi-x-circle"></i> 取消全选
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="selectReverse()">
                                        <i class="bi bi-arrow-repeat"></i> 反选
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <span class="badge bg-info me-2">已选择: <span id="selectedCount">0</span> 个工作表</span>
                                <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                                    <i class="bi bi-cloud-upload"></i> 开始导入数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工作表列表 -->
                <div class="row g-3">
                    {% for worksheet in worksheets %}
                    <div class="col-sm-6 col-md-4 col-lg-3">
                        <div class="card h-100 worksheet-card border shadow-sm" style="transition: all 0.2s ease; border-width: 1.5px !important;">
                            <div class="card-header bg-light border-bottom py-2">
                                <div class="form-check">
                                    <input class="form-check-input worksheet-checkbox" 
                                           type="checkbox" 
                                           name="selected_sheets" 
                                           value="{{ worksheet.name }}" 
                                           id="sheet_{{ loop.index }}">
                                    <label class="form-check-label fw-bold small" for="sheet_{{ loop.index }}" style="line-height: 1.2;">
                                        {{ worksheet.name }}
                                    </label>
                                </div>
                            </div>
                            <div class="card-body p-3">
                                <!-- 工作表信息 -->
                                <div class="row mb-2">
                                    <div class="col-6">
                                        <div class="text-muted small" style="font-size: 0.75rem;">模拟日期</div>
                                        <div class="fw-semibold text-primary small">{{ worksheet.simulation_date }}</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-muted small" style="font-size: 0.75rem;">时段</div>
                                        <div class="fw-semibold">
                                            <span class="badge badge-sm bg-{% if worksheet.time_period == '上午' %}success{% else %}warning{% endif %}" style="font-size: 0.7rem; padding: 2px 6px;">
                                                {{ worksheet.time_period }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 数据统计 -->
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted" style="font-size: 0.75rem;">数据行数</small>
                                    <span class="badge bg-secondary" style="font-size: 0.7rem; padding: 2px 6px;">{{ worksheet.data_rows }} 条记录</span>
                                </div>

                                <!-- 原始日期信息 -->
                                {% if worksheet.raw_date_info != worksheet.name %}
                                <div class="mb-2">
                                    <div class="text-muted small" style="font-size: 0.75rem;">原始信息</div>
                                    <div class="small text-dark" style="font-size: 0.8rem; line-height: 1.2;">{{ worksheet.raw_date_info }}</div>
                                </div>
                                {% endif %}

                                <!-- 数据预览 -->
                                {% if worksheet.preview_data %}
                                <div class="accordion accordion-flush" id="accordion_{{ loop.index }}">
                                    <div class="accordion-item border-0">
                                        <h2 class="accordion-header" id="heading_{{ loop.index }}">
                                            <button class="accordion-button collapsed border-0 p-1 bg-transparent shadow-none" 
                                                    type="button" 
                                                    data-bs-toggle="collapse" 
                                                    data-bs-target="#collapse_{{ loop.index }}"
                                                    style="font-size: 0.8rem;">
                                                <i class="bi bi-eye me-1"></i> 数据预览
                                            </button>
                                        </h2>
                                        <div id="collapse_{{ loop.index }}" 
                                             class="accordion-collapse collapse" 
                                             data-bs-parent="#accordion_{{ loop.index }}">
                                            <div class="accordion-body p-1">
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-striped mb-0">
                                                        <tbody>
                                                            {% for row in worksheet.preview_data[:3] %}
                                                            <tr>
                                                                {% for key, value in row.items() %}
                                                                <td class="small py-1" style="font-size: 0.7rem;">{{ value if value != None else '' }}</td>
                                                                {% endfor %}
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 底部操作区域 -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary me-3">
                            <i class="bi bi-arrow-left"></i> 返回上传页面
                        </a>
                        <button type="submit" class="btn btn-success btn-lg" id="submitBtnBottom" disabled>
                            <i class="bi bi-cloud-upload"></i> 导入选中的工作表
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 工作表卡片样式优化 - 三种独立状态 */

/* 状态1: 未选择默认状态 - 加强可见性 */
.worksheet-card {
    border-color: #c0c4c8 !important;
    border-width: 1.5px !important;
    background-color: #fafbfc !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    user-select: none;
}

.worksheet-card .card-header {
    background-color: #f1f3f5 !important;
    border-bottom-color: #e1e5e9 !important;
}

/* 状态2: 未选择悬停状态 - 蓝色提示 */
.worksheet-card:not(.border-primary):hover {
    border-color: #4dabf7 !important;
    background-color: #f0f8ff !important;
    box-shadow: 0 6px 20px rgba(77, 171, 247, 0.2) !important;
    transform: translateY(-3px) scale(1.02);
}

.worksheet-card:not(.border-primary):hover .card-header {
    background-color: #e7f5ff !important;
    border-bottom-color: #4dabf7 !important;
}

/* 状态3: 选择状态 - 强烈的选中效果 */
.worksheet-card.border-primary {
    border-color: #007bff !important;
    border-width: 2px !important;
    background-color: #f8f9ff !important;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3) !important;
    transform: translateY(-1px);
}

.worksheet-card.border-primary .card-header {
    background-color: #e3f2fd !important;
    border-bottom-color: #007bff !important;
    position: relative;
}

/* 选中状态的选择指示器 */
.worksheet-card.border-primary .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #4dabf7);
    border-radius: 0.375rem 0.375rem 0 0;
}

/* 选中状态悬停时保持选中样式 */
.worksheet-card.border-primary:hover {
    border-color: #0056b3 !important;
    background-color: #f0f7ff !important;
    box-shadow: 0 10px 30px rgba(0, 86, 179, 0.4) !important;
    transform: translateY(-2px) scale(1.01);
}

/* 紧凑布局优化 */
.row.g-3 > * {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
    margin-bottom: 1rem;
}

/* 复选框样式优化 - 状态相关 */
.form-check-input {
    transition: all 0.3s ease;
    border-width: 1.5px;
}

/* 未选择状态的复选框 */
.worksheet-card:not(.border-primary) .form-check-input {
    border-color: #adb5bd;
    background-color: #ffffff;
}

/* 悬停状态的复选框 */
.worksheet-card:not(.border-primary):hover .form-check-input {
    border-color: #4dabf7;
    background-color: #f0f8ff;
    transform: scale(1.1);
}

/* 选中状态的复选框 */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
    transform: scale(1.1);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 选中卡片的复选框标签 */
.worksheet-card.border-primary .form-check-label {
    color: #007bff !important;
    font-weight: 600;
}

.worksheet-card:not(.border-primary):hover .form-check-label {
    color: #4dabf7 !important;
}

/* 卡片标题区域优化 */
.worksheet-card .card-header {
    border-bottom-width: 1px;
    padding: 0.5rem 0.75rem;
}

.worksheet-card .card-header .form-check-label {
    margin-bottom: 0;
    cursor: pointer;
    display: block;
    word-break: break-word;
}

/* 徽章样式优化 - 状态相关 */
.badge {
    font-weight: 500;
    letter-spacing: 0.02em;
    transition: all 0.3s ease;
}

/* 未选择状态的徽章 */
.worksheet-card:not(.border-primary) .badge {
    opacity: 0.9;
}

/* 悬停状态的徽章 */
.worksheet-card:not(.border-primary):hover .badge {
    opacity: 1;
    transform: scale(1.05);
}

/* 选中状态的徽章 */
.worksheet-card.border-primary .badge {
    opacity: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 文字颜色优化 */
.worksheet-card:not(.border-primary) .text-primary {
    color: #495057 !important;
}

.worksheet-card:not(.border-primary):hover .text-primary {
    color: #4dabf7 !important;
}

.worksheet-card.border-primary .text-primary {
    color: #007bff !important;
    font-weight: 600;
}

/* 手机端优化 */
@media (max-width: 576px) {
    .row.g-3 > * {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
    
    .worksheet-card .card-body {
        padding: 0.75rem !important;
    }
}

/* 数据预览区域优化 */
.accordion-button {
    font-size: 0.8rem !important;
    padding: 0.25rem 0 !important;
    color: #6c757d;
    cursor: pointer !important;
}

.accordion-button:not(.collapsed) {
    color: #007bff;
    background-color: transparent;
    box-shadow: none;
}

.accordion-button::after {
    background-size: 0.8rem;
}

/* 卡片点击交互优化 */
.worksheet-card:active {
    transform: scale(0.98) !important;
}

/* 防止数据预览区域触发卡片选择 */
.accordion-button:hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

/* 复选框区域鼠标样式 */
.form-check {
    cursor: pointer;
}

.form-check-input {
    cursor: pointer;
}

.form-check-label {
    cursor: pointer;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.worksheet-checkbox');
    const submitBtn = document.getElementById('submitBtn');
    const submitBtnBottom = document.getElementById('submitBtnBottom');
    const selectedCount = document.getElementById('selectedCount');
    const worksheetCards = document.querySelectorAll('.worksheet-card');

    // 更新选择状态
    function updateSelection() {
        const checkedCount = document.querySelectorAll('.worksheet-checkbox:checked').length;
        selectedCount.textContent = checkedCount;
        
        const hasSelection = checkedCount > 0;
        submitBtn.disabled = !hasSelection;
        submitBtnBottom.disabled = !hasSelection;
        
        // 更新卡片样式
        checkboxes.forEach((checkbox, index) => {
            const card = worksheetCards[index];
            if (checkbox.checked) {
                card.classList.add('border-primary', 'bg-light');
            } else {
                card.classList.remove('border-primary', 'bg-light');
            }
        });
    }

    // 绑定复选框事件
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelection);
    });

    // 绑定卡片点击事件
    worksheetCards.forEach((card, index) => {
        card.addEventListener('click', function(e) {
            // 排除不应该触发选择的元素
            const excludedSelectors = [
                '.form-check-input',           // 复选框
                '.form-check-label',          // 复选框标签
                '.accordion-button',          // 数据预览按钮
                '.accordion-body',            // 数据预览内容
                '.accordion-body *',          // 数据预览内容的子元素
                '.table',                     // 预览表格
                '.table *'                    // 预览表格的子元素
            ];
            
            // 检查点击的元素是否在排除列表中
            let shouldExclude = false;
            for (let selector of excludedSelectors) {
                if (e.target.matches(selector) || e.target.closest(selector)) {
                    shouldExclude = true;
                    break;
                }
            }
            
            // 如果点击的是排除元素，不处理
            if (shouldExclude) return;
            
            // 切换对应的复选框状态
            const checkbox = checkboxes[index];
            checkbox.checked = !checkbox.checked;
            
            // 触发change事件以更新界面
            checkbox.dispatchEvent(new Event('change'));
            
            // 添加点击反馈动画
            card.style.transform = card.style.transform + ' scale(0.98)';
            setTimeout(() => {
                card.style.transform = card.style.transform.replace(' scale(0.98)', '');
            }, 150);
        });
    });

    // 初始化
    updateSelection();
});

// 全选功能
function selectAll() {
    document.querySelectorAll('.worksheet-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelection();
}

// 取消全选
function selectNone() {
    document.querySelectorAll('.worksheet-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelection();
}

// 反选
function selectReverse() {
    document.querySelectorAll('.worksheet-checkbox').forEach(checkbox => {
        checkbox.checked = !checkbox.checked;
    });
    updateSelection();
}

// 更新选择状态（全局函数）
function updateSelection() {
    const checkboxes = document.querySelectorAll('.worksheet-checkbox');
    const selectedCount = document.getElementById('selectedCount');
    const submitBtn = document.getElementById('submitBtn');
    const submitBtnBottom = document.getElementById('submitBtnBottom');
    const worksheetCards = document.querySelectorAll('.worksheet-card');
    
    const checkedCount = document.querySelectorAll('.worksheet-checkbox:checked').length;
    selectedCount.textContent = checkedCount;
    
    const hasSelection = checkedCount > 0;
    submitBtn.disabled = !hasSelection;
    submitBtnBottom.disabled = !hasSelection;
    
    // 更新卡片样式
    checkboxes.forEach((checkbox, index) => {
        const card = worksheetCards[index];
        if (checkbox.checked) {
            card.classList.add('border-primary', 'bg-light');
        } else {
            card.classList.remove('border-primary', 'bg-light');
        }
    });
}
</script>
{% endblock %} 