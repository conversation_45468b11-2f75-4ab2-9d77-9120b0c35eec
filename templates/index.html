{% extends "base.html" %}

{% block title %}数据导入 - 摩托车模拟登记表管理系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <!-- 页面标题 -->
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-upload text-primary me-2"></i>
                    Excel数据导入
                </h1>
                <div class="badge bg-info fs-6">
                    <i class="bi bi-info-circle me-1"></i>
                    支持 .xlsx 和 .xls 格式
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 文件上传区域 -->
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                    <h5 class="card-title text-white mb-0">
                        <i class="bi bi-file-earmark-excel me-2"></i>
                        上传Excel文件
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ url_for('upload_file') }}" method="post" enctype="multipart/form-data" id="uploadForm">
                        <div class="mb-4">
                            <label for="file" class="form-label fw-bold fs-5 text-primary mb-3">
                                <i class="bi bi-file-earmark-excel me-2"></i>
                                选择Excel文件
                            </label>
                            
                            <!-- 拖拽上传区域 -->
                            <div class="upload-area border border-2 border-dashed rounded-3 p-4 mb-3 text-center position-relative" 
                                 id="uploadArea" style="border-color: #0d6efd !important; background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%); min-height: 120px; transition: all 0.3s ease;">
                                
                                <div class="upload-content">
                                    <i class="bi bi-cloud-upload text-primary mb-2" style="font-size: 2.5rem;"></i>
                                    <h6 class="text-primary fw-bold mb-2">拖拽文件到此处或点击选择</h6>
                                    <p class="text-muted small mb-3">支持 .xlsx 和 .xls 格式，最大16MB</p>
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <button type="button" class="btn btn-outline-primary btn-lg px-4 me-md-2" id="selectFileBtn">
                                            <i class="bi bi-folder2-open me-2"></i>
                                            选择文件
                                        </button>
                                        <button type="submit" class="btn btn-success btn-lg px-4" id="uploadBtn" disabled>
                                            <i class="bi bi-cloud-upload me-2"></i>
                                            开始导入
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 隐藏的文件输入 -->
                                <input type="file" class="d-none" id="file" name="file" 
                                       accept=".xlsx,.xls" required>
                            </div>
                            
                            <!-- 文件选择状态提示 -->
                            <div class="form-text d-flex align-items-center">
                                <i class="bi bi-info-circle me-2 text-info"></i>
                                <span id="fileStatus" class="text-muted">请选择包含摩托车模拟登记数据的Excel文件</span>
                            </div>
                        </div>

                        <!-- 文件预览信息 -->
                        <div id="fileInfo" class="alert alert-info d-none">
                            <h6 class="alert-heading">
                                <i class="bi bi-file-check me-1"></i>
                                文件信息
                            </h6>
                            <div id="fileDetails"></div>
                        </div>

                        <!-- 上传进度 -->
                        <div id="uploadProgress" class="d-none">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%">
                                    <span class="progress-text">准备上传...</span>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 使用说明 -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="fw-bold text-primary">
                            <i class="bi bi-question-circle me-1"></i>
                            使用说明
                        </h6>
                        <ul class="mb-0 small">
                            <li>Excel文件应包含摩托车科二、三模拟登记表数据</li>
                            <li>支持多个工作表，每个工作表会自动解析日期和时段信息</li>
                            <li>数据格式：序号、驾校、教练、学员、车型、模拟科目</li>
                            <li>导入后会自动替换现有数据库内容</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
/* 自定义样式优化 */
.upload-area {
    cursor: pointer;
    position: relative;
}

.upload-area:hover {
    border-color: #0056b3 !important;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15) !important;
}

.upload-area.dragover {
    border-color: #198754 !important;
    background: linear-gradient(135deg, #d1e7dd 0%, #a3d9b1 100%) !important;
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(25, 135, 84, 0.2) !important;
}

.upload-area.file-selected {
    border-color: #198754 !important;
    background: linear-gradient(135deg, #d1e7dd 0%, #f8f9fa 100%) !important;
}

.upload-content {
    pointer-events: none;
}

.upload-content .btn {
    pointer-events: all;
}

.file-info-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    margin-top: 10px;
    box-shadow: 0 2px 10px rgba(25, 135, 84, 0.2);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('file');
    const fileInfo = document.getElementById('fileInfo');
    const fileDetails = document.getElementById('fileDetails');
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const selectFileBtn = document.getElementById('selectFileBtn');
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadArea = document.getElementById('uploadArea');
    const uploadContent = uploadArea.querySelector('.upload-content');
    const fileStatus = document.getElementById('fileStatus');

    // 点击选择文件按钮
    selectFileBtn.addEventListener('click', function() {
        fileInput.click();
    });

    // 点击上传区域也可以选择文件
    uploadArea.addEventListener('click', function(e) {
        if (e.target === uploadArea || e.target.closest('.upload-content') && !e.target.closest('.btn')) {
            fileInput.click();
        }
    });

    // 拖拽功能
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            
            // 检查文件类型
            if (file.name.toLowerCase().endsWith('.xlsx') || file.name.toLowerCase().endsWith('.xls')) {
                fileInput.files = files;
                handleFileSelection(file);
            } else {
                showFileError('请选择Excel文件（.xlsx 或 .xls 格式）');
            }
        }
    });

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFileSelection(file);
        } else {
            resetFileSelection();
        }
    });

    // 处理文件选择
    function handleFileSelection(file) {
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        
        // 检查文件大小
        if (file.size > 16 * 1024 * 1024) {
            showFileError('文件大小超过16MB限制，请选择较小的文件');
            return;
        }

        // 更新UI状态
        uploadArea.classList.add('file-selected');
        uploadBtn.disabled = false;
        
        // 更新上传区域内容
        uploadContent.innerHTML = `
            <i class="bi bi-file-earmark-excel-fill text-success mb-2" style="font-size: 3rem;"></i>
            <h6 class="text-success fw-bold mb-2">文件已选择</h6>
            <div class="file-info-badge">
                <i class="bi bi-file-check me-2"></i>
                <span>${file.name} (${fileSize} MB)</span>
            </div>
            <p class="text-muted small mt-3 mb-3">点击"开始导入"处理文件，或重新选择其他文件</p>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <button type="button" class="btn btn-outline-secondary btn-lg px-4 me-md-2" id="reselectBtn">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    重新选择
                </button>
                <button type="submit" class="btn btn-success btn-lg px-4 pulse-animation" id="uploadBtn">
                    <i class="bi bi-cloud-upload me-2"></i>
                    开始导入
                </button>
            </div>
        `;

        // 重新绑定按钮事件
        const reselectBtn = uploadContent.querySelector('#reselectBtn');
        reselectBtn.addEventListener('click', function() {
            fileInput.value = '';
            resetFileSelection();
        });

        // 更新状态文本
        fileStatus.innerHTML = `<i class="bi bi-check-circle text-success me-2"></i>已选择文件：${file.name}`;
        
        // 显示文件详细信息
        fileDetails.innerHTML = `
            <strong>文件名：</strong>${file.name}<br>
            <strong>文件大小：</strong>${fileSize} MB<br>
            <strong>文件类型：</strong>${file.type || 'Excel文件'}
        `;
        fileInfo.classList.remove('d-none');
    }

    // 重置文件选择
    function resetFileSelection() {
        uploadArea.classList.remove('file-selected');
        uploadBtn.disabled = true;
        
        // 恢复原始上传区域内容
        uploadContent.innerHTML = `
            <i class="bi bi-cloud-upload text-primary mb-2" style="font-size: 2.5rem;"></i>
            <h6 class="text-primary fw-bold mb-2">拖拽文件到此处或点击选择</h6>
            <p class="text-muted small mb-3">支持 .xlsx 和 .xls 格式，最大16MB</p>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <button type="button" class="btn btn-outline-primary btn-lg px-4 me-md-2" id="selectFileBtn">
                    <i class="bi bi-folder2-open me-2"></i>
                    选择文件
                </button>
                <button type="submit" class="btn btn-success btn-lg px-4" id="uploadBtn" disabled>
                    <i class="bi bi-cloud-upload me-2"></i>
                    开始导入
                </button>
            </div>
        `;

        // 重新绑定选择文件按钮
        const newSelectBtn = uploadContent.querySelector('#selectFileBtn');
        newSelectBtn.addEventListener('click', function() {
            fileInput.click();
        });

        // 重置状态文本和文件信息
        fileStatus.innerHTML = `请选择包含摩托车模拟登记数据的Excel文件`;
        fileInfo.classList.add('d-none');
    }

    // 显示文件错误
    function showFileError(message) {
        fileStatus.innerHTML = `<i class="bi bi-exclamation-triangle text-danger me-2"></i>${message}`;
        
        // 3秒后恢复原始状态
        setTimeout(() => {
            fileStatus.innerHTML = `请选择包含摩托车模拟登记数据的Excel文件`;
        }, 3000);
    }

    // 表单提交事件
    uploadForm.addEventListener('submit', function(e) {
        const file = fileInput.files[0];
        if (!file) {
            e.preventDefault();
            showFileError('请先选择要上传的文件');
            return;
        }

        // 显示上传进度
        const currentUploadBtn = uploadContent.querySelector('#uploadBtn');
        currentUploadBtn.disabled = true;
        currentUploadBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>处理中...';
        currentUploadBtn.classList.remove('pulse-animation');
        
        uploadProgress.classList.remove('d-none');
        
        // 模拟进度更新
        let progress = 0;
        const progressBar = uploadProgress.querySelector('.progress-bar');
        const progressText = uploadProgress.querySelector('.progress-text');
        
        const updateProgress = () => {
            progress += Math.random() * 30;
            if (progress > 90) progress = 90;
            
            progressBar.style.width = progress + '%';
            if (progress < 30) {
                progressText.textContent = '上传文件...';
            } else if (progress < 60) {
                progressText.textContent = '解析数据...';
            } else {
                progressText.textContent = '保存到数据库...';
            }
        };
        
        const progressInterval = setInterval(updateProgress, 500);
        
        // 清理定时器（表单提交后页面会跳转）
        setTimeout(() => {
            clearInterval(progressInterval);
        }, 10000);
    });
});
</script>
{% endblock %} 