{% extends "base.html" %}

{% block title %}查看记录 - 摩托车模拟登记表管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作 -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-table text-primary me-2"></i>
                    数据记录
                </h1>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-info" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>
                        导出数据
                    </button>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="bi bi-upload me-1"></i>
                        导入新数据
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label fw-bold">搜索学员</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchStudent" 
                                       placeholder="输入学员姓名...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold">时段筛选</label>
                            <select class="form-select" id="filterTime">
                                <option value="">全部时段</option>
                                <option value="上午">上午</option>
                                <option value="下午">下午</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold">日期筛选</label>
                            <select class="form-select" id="filterDate">
                                <option value="">全部日期</option>
                                {% for date in unique_dates %}
                                    <option value="{{ date }}">{{ date }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold">教练筛选</label>
                            <select class="form-select" id="filterTeacher">
                                <option value="">全部教练</option>
                                {% for teacher in unique_teachers %}
                                    <option value="{{ teacher }}">{{ teacher }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="bi bi-x-circle me-1"></i>
                                    清除筛选
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计 -->
    {% if records %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info d-flex justify-content-between align-items-center">
                <div>
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>数据统计：</strong>
                    共找到 <span id="totalRecords">{{ records|length }}</span> 条记录
                    <span id="filteredInfo" class="ms-3 text-muted"></span>
                </div>
                <div class="small text-muted">
                    最后更新：{{ records[0]['创建时间'] if records else '无数据' }}
                </div>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #6c757d, #495057);">
                    <h5 class="card-title text-white mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        全部记录
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0" id="recordsTable">
                            <thead class="table-dark sticky-top">
                                <tr>
                                    <th class="text-center" style="width: 60px;">#</th>
                                    <th class="text-center" style="width: 80px;">序号</th>
                                    <th style="width: 120px;">驾校</th>
                                    <th style="width: 100px;">教练</th>
                                    <th style="width: 100px;">学员</th>
                                    <th style="width: 80px;">车型</th>
                                    <th style="width: 120px;">模拟科目</th>
                                    <th style="width: 100px;">模拟日期</th>
                                    <th style="width: 80px;">时段</th>
                                    <th style="width: 100px;">工作表</th>
                                    <th style="width: 150px;">创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in records %}
                                <tr class="record-row" 
                                    data-student="{{ record['学员'] }}"
                                    data-time="{{ record['时段'] }}"
                                    data-date="{{ record['模拟日期'] }}"
                                    data-teacher="{{ record['教练'] }}">
                                    <td class="text-center">
                                        <span class="badge bg-secondary">{{ loop.index }}</span>
                                    </td>
                                    <td class="text-center fw-bold">{{ record['序号'] }}</td>
                                    <td>
                                        <i class="bi bi-building text-primary me-1"></i>
                                        {{ record['驾校'] }}
                                    </td>
                                    <td>
                                        <i class="bi bi-person-badge text-success me-1"></i>
                                        {{ record['教练'] }}
                                    </td>
                                    <td>
                                        <i class="bi bi-person text-info me-1"></i>
                                        <strong>{{ record['学员'] }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ record['车型'] }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ record['模拟科目'] }}</small>
                                    </td>
                                    <td>
                                        <i class="bi bi-calendar3 text-warning me-1"></i>
                                        {{ record['模拟日期'] }}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'info' if record['时段'] == '上午' else 'warning' }}">
                                            <i class="bi bi-{{ 'sun' if record['时段'] == '上午' else 'moon' }} me-1"></i>
                                            {{ record['时段'] }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ record['工作表'] }}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ record['创建时间'][:16] if record['创建时间'] else '未知' }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 表格底部工具栏 -->
                <div class="card-footer bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                显示 <span id="visibleRecords">{{ records|length }}</span> 条记录
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-secondary" onclick="selectAll()">
                                    全选
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="selectNone()">
                                    取消选择
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteSelected()" disabled>
                                    删除选中
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- 无数据状态 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-body text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无数据记录</h4>
                    <p class="text-muted">请先导入Excel文件以创建数据记录</p>
                    <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg mt-3">
                        <i class="bi bi-upload me-2"></i>
                        开始导入数据
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchStudent');
    const filterTime = document.getElementById('filterTime');
    const filterDate = document.getElementById('filterDate');
    const filterTeacher = document.getElementById('filterTeacher');
    const recordRows = document.querySelectorAll('.record-row');
    const totalRecords = document.getElementById('totalRecords');
    const visibleRecords = document.getElementById('visibleRecords');
    const filteredInfo = document.getElementById('filteredInfo');

    // 筛选功能
    function filterRecords() {
        const searchText = searchInput.value.toLowerCase();
        const timeFilter = filterTime.value;
        const dateFilter = filterDate.value;
        const teacherFilter = filterTeacher.value;
        
        let visibleCount = 0;
        
        recordRows.forEach(row => {
            const student = row.dataset.student.toLowerCase();
            const time = row.dataset.time;
            const date = row.dataset.date;
            const teacher = row.dataset.teacher;
            
            const matchSearch = !searchText || student.includes(searchText);
            const matchTime = !timeFilter || time === timeFilter;
            const matchDate = !dateFilter || date === dateFilter;
            const matchTeacher = !teacherFilter || teacher === teacherFilter;
            
            if (matchSearch && matchTime && matchDate && matchTeacher) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        // 更新显示数量
        visibleRecords.textContent = visibleCount;
        
        // 显示筛选信息
        const filters = [];
        if (searchText) filters.push(`搜索: "${searchInput.value}"`);
        if (timeFilter) filters.push(`时段: ${timeFilter}`);
        if (dateFilter) filters.push(`日期: ${dateFilter}`);
        if (teacherFilter) filters.push(`教练: ${teacherFilter}`);
        
        if (filters.length > 0) {
            filteredInfo.textContent = `筛选条件: ${filters.join(', ')}`;
        } else {
            filteredInfo.textContent = '';
        }
    }

    // 自动选择当天日期
    function autoSelectTodayDate() {
        const today = new Date();
        const todayFormatted = `${today.getMonth() + 1}/${today.getDate()}`;
        
        // 查找匹配当天日期的选项
        const dateOptions = filterDate.querySelectorAll('option');
        for (let option of dateOptions) {
            if (option.value === todayFormatted) {
                filterDate.value = todayFormatted;
                // 触发筛选，显示当天的记录
                filterRecords();
                break;
            }
        }
    }
    
    // 页面加载完成后自动选择当天日期
    autoSelectTodayDate();

    // 绑定事件
    searchInput.addEventListener('input', filterRecords);
    filterTime.addEventListener('change', filterRecords);
    filterDate.addEventListener('change', filterRecords);
    filterTeacher.addEventListener('change', filterRecords);

    // 实时搜索高亮
    searchInput.addEventListener('input', function() {
        const searchText = this.value.toLowerCase();
        recordRows.forEach(row => {
            const studentCell = row.querySelector('td:nth-child(5)');
            const originalText = studentCell.dataset.original || studentCell.textContent;
            studentCell.dataset.original = originalText;
            
            if (searchText && originalText.toLowerCase().includes(searchText)) {
                const regex = new RegExp(`(${searchText})`, 'gi');
                const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
                studentCell.innerHTML = studentCell.innerHTML.replace(originalText, highlightedText);
            } else {
                studentCell.innerHTML = originalText;
            }
        });
    });
});

// 清除筛选
function clearFilters() {
    document.getElementById('searchStudent').value = '';
    document.getElementById('filterTime').value = '';
    document.getElementById('filterTeacher').value = '';
    
    // 清除高亮
    const recordRows = document.querySelectorAll('.record-row');
    recordRows.forEach(row => {
        const studentCell = row.querySelector('td:nth-child(5)');
        if (studentCell.dataset.original) {
            studentCell.innerHTML = studentCell.dataset.original;
        }
    });
    
    // 重新自动选择当天日期
    const today = new Date();
    const todayFormatted = `${today.getMonth() + 1}/${today.getDate()}`;
    const filterDate = document.getElementById('filterDate');
    
    // 查找匹配当天日期的选项
    const dateOptions = filterDate.querySelectorAll('option');
    let todayFound = false;
    for (let option of dateOptions) {
        if (option.value === todayFormatted) {
            filterDate.value = todayFormatted;
            todayFound = true;
            break;
        }
    }
    
    // 如果没有找到当天日期，则清空日期筛选
    if (!todayFound) {
        filterDate.value = '';
    }
    
    // 触发筛选以更新显示
    const searchInput = document.getElementById('searchStudent');
    const filterTime = document.getElementById('filterTime');
    const filterTeacher = document.getElementById('filterTeacher');
    const totalRecords = document.getElementById('totalRecords');
    const visibleRecords = document.getElementById('visibleRecords');
    const filteredInfo = document.getElementById('filteredInfo');
    
    const timeFilter = filterTime.value;
    const dateFilter = filterDate.value;
    const teacherFilter = filterTeacher.value;
    
    let visibleCount = 0;
    
    recordRows.forEach(row => {
        const time = row.dataset.time;
        const date = row.dataset.date;
        const teacher = row.dataset.teacher;
        
        const matchTime = !timeFilter || time === timeFilter;
        const matchDate = !dateFilter || date === dateFilter;
        const matchTeacher = !teacherFilter || teacher === teacherFilter;
        
        if (matchTime && matchDate && matchTeacher) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // 更新显示数量
    visibleRecords.textContent = visibleCount;
    
    // 显示筛选信息
    const filters = [];
    if (timeFilter) filters.push(`时段: ${timeFilter}`);
    if (dateFilter) filters.push(`日期: ${dateFilter}`);
    if (teacherFilter) filters.push(`教练: ${teacherFilter}`);
    
    if (filters.length > 0) {
        filteredInfo.textContent = `筛选条件: ${filters.join(', ')}`;
    } else {
        filteredInfo.textContent = '';
    }
}

// 导出数据
function exportData() {
    // 获取可见的记录
    const visibleRows = Array.from(document.querySelectorAll('.record-row'))
        .filter(row => row.style.display !== 'none');
    
    if (visibleRows.length === 0) {
        alert('没有可导出的数据');
        return;
    }
    
    // 构建CSV内容
    const headers = ['序号', '驾校', '教练', '学员', '车型', '模拟科目', '模拟日期', '时段', '工作表', '创建时间'];
    let csvContent = headers.join(',') + '\n';
    
    visibleRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const rowData = [
            cells[1].textContent.trim(), // 序号
            cells[2].textContent.replace(/\s+/g, ' ').trim(), // 驾校
            cells[3].textContent.replace(/\s+/g, ' ').trim(), // 教练
            cells[4].textContent.replace(/\s+/g, ' ').trim(), // 学员
            cells[5].textContent.trim(), // 车型
            cells[6].textContent.trim(), // 模拟科目
            cells[7].textContent.replace(/\s+/g, ' ').trim(), // 模拟日期
            cells[8].textContent.replace(/\s+/g, ' ').trim(), // 时段
            cells[9].textContent.trim(), // 工作表
            cells[10].textContent.trim()  // 创建时间
        ];
        csvContent += rowData.join(',') + '\n';
    });
    
    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `摩托车模拟记录_${new Date().toISOString().slice(0, 10)}.csv`;
    link.click();
}

// 全选功能（预留）
function selectAll() {
    // TODO: 实现全选功能
    console.log('全选功能');
}

function selectNone() {
    // TODO: 实现取消选择功能
    console.log('取消选择功能');
}

function deleteSelected() {
    // TODO: 实现删除选中功能
    console.log('删除选中功能');
}
</script>
{% endblock %} 