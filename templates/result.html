{% extends "base.html" %}

{% block title %}导入结果 - 摩托车模拟登记表管理系统{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题 -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                    导入完成
                </h1>
                <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left me-1"></i>
                    返回导入页面
                </a>
            </div>
        </div>
    </div>

    <!-- 导入结果统计 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up me-2"></i>
                        导入统计结果
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <!-- 总记录数 -->
                        <div class="col-md-3">
                            <div class="card border-0 bg-primary bg-opacity-10">
                                <div class="card-body">
                                    <i class="bi bi-file-earmark-text text-primary" style="font-size: 2rem;"></i>
                                    <h3 class="text-primary mt-2">{{ result.total_count }}</h3>
                                    <p class="text-muted mb-0">总记录数</p>
                                </div>
                            </div>
                        </div>

                        <!-- 时段统计 -->
                        {% if result.time_stats %}
                        {% for time_period, count in result.time_stats %}
                        <div class="col-md-3">
                            <div class="card border-0 bg-info bg-opacity-10">
                                <div class="card-body">
                                    <i class="bi bi-clock text-info" style="font-size: 2rem;"></i>
                                    <h3 class="text-info mt-2">{{ count }}</h3>
                                    <p class="text-muted mb-0">{{ time_period }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% endif %}

                        <!-- 驾校统计 -->
                        {% if result.school_stats %}
                        {% for school, count in result.school_stats %}
                        <div class="col-md-3">
                            <div class="card border-0 bg-warning bg-opacity-10">
                                <div class="card-body">
                                    <i class="bi bi-building text-warning" style="font-size: 2rem;"></i>
                                    <h3 class="text-warning mt-2">{{ count }}</h3>
                                    <p class="text-muted mb-0">{{ school }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% endif %}
                    </div>

                    <!-- 成功消息 -->
                    <div class="alert alert-success mt-4" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <strong>导入成功！</strong> {{ result.message }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据预览 -->
    {% if data_preview %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #6f42c1, #4e2a8e);">
                    <h5 class="card-title text-white mb-0">
                        <i class="bi bi-eye me-2"></i>
                        数据预览 (前10条记录)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center">#</th>
                                    <th>序号</th>
                                    <th>驾校</th>
                                    <th>教练</th>
                                    <th>学员</th>
                                    <th>车型</th>
                                    <th>模拟科目</th>
                                    <th>模拟日期</th>
                                    <th>时段</th>
                                    <th>工作表</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in data_preview %}
                                <tr>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">{{ loop.index }}</span>
                                    </td>
                                    <td>{{ record['序号'] }}</td>
                                    <td>
                                        <i class="bi bi-building me-1"></i>
                                        {{ record['驾校'] }}
                                    </td>
                                    <td>
                                        <i class="bi bi-person-badge me-1"></i>
                                        {{ record['教练'] }}
                                    </td>
                                    <td>
                                        <i class="bi bi-person me-1"></i>
                                        {{ record['学员'] }}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ record['车型'] }}</span>
                                    </td>
                                    <td>{{ record['模拟科目'] }}</td>
                                    <td>
                                        <i class="bi bi-calendar3 me-1"></i>
                                        {{ record['模拟日期'] }}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'info' if record['时段'] == '上午' else 'warning' }}">
                                            {{ record['时段'] }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ record['工作表'] }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 查看更多按钮 -->
                    {% if result.total_count > 10 %}
                    <div class="card-footer bg-light text-center">
                        <p class="text-muted mb-2">
                            显示前10条记录，共 {{ result.total_count }} 条记录
                        </p>
                        <a href="{{ url_for('view_records') }}" class="btn btn-primary">
                            <i class="bi bi-table me-1"></i>
                            查看所有记录
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 操作按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-center gap-3">
                <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg">
                    <i class="bi bi-upload me-1"></i>
                    继续导入数据
                </a>
                <a href="{{ url_for('view_records') }}" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-table me-1"></i>
                    查看所有记录
                </a>
                <a href="{{ url_for('api_stats') }}" target="_blank" class="btn btn-outline-info btn-lg">
                    <i class="bi bi-api me-1"></i>
                    API统计
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 自动滚动到结果页面顶部
    window.scrollTo(0, 0);
    
    // 添加成功动画效果
    const successIcon = document.querySelector('.bi-check-circle-fill');
    if (successIcon) {
        successIcon.style.animation = 'bounceIn 1s ease-in-out';
    }
    
    // 为统计卡片添加悬停效果
    const statCards = document.querySelectorAll('.card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});

// CSS动画定义
const style = document.createElement('style');
style.textContent = `
    @keyframes bounceIn {
        0% {
            opacity: 0;
            transform: scale(0.3);
        }
        50% {
            opacity: 1;
            transform: scale(1.05);
        }
        70% {
            transform: scale(0.9);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %} 