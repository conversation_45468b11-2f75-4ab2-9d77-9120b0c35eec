<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份证读取识别比对系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            justify-content: center;
            padding: 30px;
        }

        .main-content .card {
            max-width: 800px;
            width: 100%;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            box-sizing: border-box;
            min-height: 600px;
        }

        .card-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
        }

        .form-group {
            margin-bottom: 20px;
            box-sizing: border-box;
        }

        .form-label {
            display: block;
            font-weight: bold;
            color: #555;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-sizing: border-box;
            min-height: 44px;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.2);
        }

        .form-input:read-only {
            background-color: #f8f9fa;
            color: #666;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #229954, #1e8449);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e67e22, #d35400);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background-color: #27ae60;
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
        }

        .status-error {
            background-color: #e74c3c;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
        }

        .status-warning {
            background-color: #f39c12;
            box-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
        }

        .result-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 150px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-y: auto;
        }

        .result-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-color: #c3e6cb;
            color: #155724;
        }

        .result-error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border-color: #f5c6cb;
            color: #721c24;
        }

        .result-warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-color: #ffeaa7;
            color: #856404;
        }

        .photo-display {
            width: 120px;
            height: 120px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
            margin: 0;
            position: relative;
            overflow: hidden;
            flex-shrink: 0; /* 防止收缩 */
            box-sizing: border-box; /* 确保边框包含在尺寸内 */
            min-width: 120px; /* 确保最小宽度 */
            max-width: 120px; /* 确保最大宽度 */
        }

        .photo-display img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 6px;
        }

        /* 确保顶部网格布局稳定 */
        .top-grid {
            display: grid;
            grid-template-columns: 2fr 130px;
            gap: 15px;
            margin-bottom: 20px;
            align-items: start;
            min-height: 200px; /* 增加高度以容纳更多内容 */
        }

        .top-grid .form-group {
            margin-bottom: 0;
            min-width: 0; /* 允许内容收缩 */
        }

        /* 左侧信息区域布局 */
        .left-info-area {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            align-items: start;
        }

        /* 个人信息四项网格 */
        .personal-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
            align-items: stretch;
            width: 100%;
            box-sizing: border-box;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            min-height: 50px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
        }

        .comparison-result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #ddd;
        }

        .match-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-color: #c3e6cb;
        }

        .match-failed {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border-color: #f5c6cb;
        }

        .loading {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            z-index: 1000;
            border-radius: 8px;
            backdrop-filter: blur(3px);
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .loading.show {
            opacity: 1;
        }

        .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .spinner {
            border: 3px solid #e3f2fd;
            border-top: 3px solid #2196f3;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            color: #2c3e50;
            font-size: 15px;
            font-weight: 500;
            margin: 0;
            white-space: nowrap;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .auto-read-status {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .auto-read-status.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 2px solid #28a745;
            box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);
        }

        .auto-read-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
            animation: pulse 2s infinite;
        }

        .status-dot.waiting {
            background: #ffc107;
        }

        .status-dot.detecting {
            background: #17a2b8;
            animation: pulse 1s infinite;
        }

        .status-dot.reading {
            background: #007bff;
            animation: pulse 0.5s infinite;
        }

        .status-dot.success {
            background: #28a745;
            animation: none;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.6);
            border: 2px solid #ffffff;
        }

        .status-dot.error {
            background: #dc3545;
            animation: none;
        }

        .status-text {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
        }

        .status-text.success {
            color: #28a745;
            font-weight: 700;
        }

        .auto-read-hint {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }



        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .info-grid {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            /* 移动端顶部网格调整为单列 */
            .top-grid {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
                min-height: auto !important;
            }

            /* 移动端左侧信息区域调整 */
            .left-info-area {
                grid-template-columns: 1fr !important;
                gap: 10px !important;
            }

            /* 移动端个人信息网格保持2列 */
            .personal-info-grid {
                grid-template-columns: 1fr 1fr !important;
                gap: 10px !important;
                margin-top: 10px !important;
            }

            .photo-display {
                width: 100px;
                height: 100px;
                margin: 0 auto;
                flex-shrink: 0;
            }
        }

        @media (max-width: 480px) {
            /* 超小屏幕调整为单列 */
            .main-content .card:first-child > div:nth-of-type(2) {
                grid-template-columns: 1fr !important;
            }

            /* 保持签发机关和有效期限在一行，但调整字体大小 */
            .info-grid {
                grid-template-columns: 1fr 1fr;
                gap: 8px;
            }

            .info-item {
                padding: 10px;
            }

            .info-label {
                font-size: 11px;
            }

            .info-value {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🆔 身份证读取识别系统</h1>
            <p>基于CVR100U读卡器 | 高效身份证信息读取</p>
        </div>



        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 左侧：身份证读取区域 -->
            <div class="card" style="position: relative;">
                <div class="card-title">
                    <span class="status-indicator" id="readerStatus"></span>
                    身份证读取区域
                </div>

                <!-- 自动读取状态显示 -->
                <div style="text-align: center; margin-bottom: 20px;">
                    <div class="auto-read-status" id="autoReadStatus">
                        <div class="auto-read-indicator">
                            <span class="status-dot" id="statusDot"></span>
                            <span class="status-text" id="statusText">等待放置证件...</span>
                        </div>
                        <div class="auto-read-hint">请将身份证放置在读卡器上</div>
                    </div>
                </div>



                <!-- 进度条 -->
                <div class="progress-bar">
                    <div class="progress-fill" id="progressBar"></div>
                </div>

                <!-- 加载状态覆盖层 -->
                <div class="loading" id="loadingIndicator">
                    <div class="loading-content">
                        <div class="spinner"></div>
                        <div class="loading-text">正在读取身份证...</div>
                    </div>
                </div>

                <!-- 身份证信息表单 -->
                <!-- 顶部区域：左侧信息区域和照片 -->
                <div class="top-grid">
                    <!-- 左侧信息区域 -->
                    <div class="left-info-area">
                        <!-- 证件类型和身份证号码 -->
                        <div class="form-group">
                            <label class="form-label">证件类型</label>
                            <input type="text" class="form-input" id="certType" readonly>
                        </div>

                        <div class="form-group">
                            <label class="form-label">身份证号码</label>
                            <input type="text" class="form-input" id="idNumber" readonly>
                        </div>

                        <!-- 个人信息四项：姓名、性别、民族、出生日期 -->
                        <div class="personal-info-grid" style="grid-column: 1 / -1;">
                            <div class="info-item">
                                <div class="info-label">姓名</div>
                                <div class="info-value" id="name">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">性别</div>
                                <div class="info-value" id="gender">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">民族</div>
                                <div class="info-value" id="nation">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">出生日期</div>
                                <div class="info-value" id="birthday">-</div>
                            </div>
                        </div>
                    </div>

                    <!-- 照片显示区域 -->
                    <div class="photo-display" id="photoDisplay">
                        暂无照片
                    </div>
                </div>

                <!-- 隐藏的读取时间组件 -->
                <div class="form-group" style="display: none;">
                    <label class="form-label">读取时间</label>
                    <input type="text" class="form-input" id="readTime" readonly>
                </div>

                <div class="form-group">
                    <label class="form-label">住址</label>
                    <input type="text" class="form-input" id="address" readonly>
                </div>

                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">签发机关</div>
                        <div class="info-value" id="issueOrg">-</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">有效期限</div>
                        <div class="info-value" id="validPeriod">-</div>
                    </div>
                </div>


            </div>


        </div>
    </div>

    <script>
        // 全局变量
        let autoReadMode = true; // 自动读取模式
        let isCardPresent = false; // 证件是否在位
        let lastReadTime = 0; // 上次读取时间
        let autoReadInterval = null; // 自动检测定时器
        let cardDetectionCount = 0; // 连续检测到证件的次数
        let detectionStartTime = 0; // 检测开始时间



        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateReaderStatus('ready');
            updateAutoReadStatus('waiting', '等待放置证件...', '请将身份证放置在读卡器上');
            if (autoReadMode) {
                startAutoDetection();
            }
        });

        // 更新读卡器状态
        function updateReaderStatus(status) {
            const indicator = document.getElementById('readerStatus');
            indicator.className = 'status-indicator';

            switch(status) {
                case 'ready':
                    indicator.classList.add('status-success');
                    break;
                case 'reading':
                    indicator.classList.add('status-warning');
                    break;
                case 'completed':
                    indicator.classList.add('status-success');
                    // 添加完成状态的特殊样式
                    indicator.style.boxShadow = '0 0 10px rgba(40, 167, 69, 0.5)';
                    break;
                case 'error':
                    indicator.classList.add('status-error');
                    indicator.style.boxShadow = 'none';
                    break;
                default:
                    indicator.style.boxShadow = 'none';
                    break;
            }
        }

        // 更新比对状态
        function updateCompareStatus(status) {
            const indicator = document.getElementById('compareStatus');
            indicator.className = 'status-indicator';
            
            switch(status) {
                case 'ready':
                    indicator.classList.add('status-success');
                    break;
                case 'comparing':
                    indicator.classList.add('status-warning');
                    break;
                case 'error':
                    indicator.classList.add('status-error');
                    break;
            }
        }



        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }

        // 显示加载状态
        function showLoading(show) {
            const loadingElement = document.getElementById('loadingIndicator');
            if (show) {
                loadingElement.style.display = 'block';
                // 强制重绘以确保transition生效
                loadingElement.offsetHeight;
                loadingElement.classList.add('show');
            } else {
                loadingElement.classList.remove('show');
                // 等待动画完成后隐藏元素
                setTimeout(() => {
                    if (!loadingElement.classList.contains('show')) {
                        loadingElement.style.display = 'none';
                    }
                }, 300);
            }
        }

        // 控制按钮启用/禁用状态
        function setButtonsEnabled(enabled) {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.disabled = !enabled;
                if (enabled) {
                    button.style.opacity = '1';
                    button.style.cursor = 'pointer';
                    button.style.filter = 'none';
                    button.style.transform = 'none';
                } else {
                    button.style.opacity = '0.5';
                    button.style.cursor = 'not-allowed';
                    button.style.filter = 'grayscale(50%)';
                    button.style.transform = 'scale(0.98)';
                }
            });
        }



        // AJAX函数
        function ajax(options) {
            if(options.type==null) options.type="POST";
            if(options.url==null) options.url="";
            if(options.timeout==null) options.timeout=5000;
            if(options.onComplate==null) options.onComplate=function() {}
            if(options.onError==null) options.onError=function() {}
            if(options.onSuccess==null) options.onSuccess=function() {}
            if(options.data) options.data="";

            var xml;
            if (typeof ActiveXObject != 'undefined') {
                var aVersions = ["Microsoft.XMLHTTP", "Msxml2.XMLHttp.6.0", "Msxml2.XMLHttp.5.0", "Msxml2.XMLHttp.4.0", "Msxml2.XMLHttp.3.0"];
                for (var i = 0; i < aVersions.length; i++) {
                    try {
                        xml = new ActiveXObject(aVersions[i]);
                    }catch (e) {}
                }
            } else if (typeof XMLHttpRequest != 'undefined'){
                xml = new XMLHttpRequest();
            }

            console.log('AJAX - 打开连接:', options.type, options.url);
            xml.open(options.type, options.url, true);

            var timeoutLength = options.timeout;
            var requestDone = false;

            setTimeout(function() {
                console.log('AJAX - 请求超时');
                requestDone = true;
            }, timeoutLength);

            xml.onreadystatechange = function() {
                console.log('AJAX状态变化 - readyState:', xml.readyState, 'requestDone:', requestDone);
                if (xml.readyState == 4 && !requestDone) {
                    console.log('AJAX请求完成 - status:', xml.status, 'statusText:', xml.statusText);
                    if (httpSuccess(xml)) {
                        console.log('AJAX请求成功，调用onSuccess');
                        var responseData = httpData(xml);
                        console.log('响应数据:', responseData);
                        options.onSuccess(responseData);
                    } else {
                        console.log('AJAX请求失败，调用onError');
                        options.onError();
                    }
                    options.onComplate();
                    xml = null;
                }
            };

            console.log('AJAX - 发送请求');
            xml.send();

            function httpSuccess(r) {
                try {
                    return !r.status && location.protocol == "file:"
                            ||
                            (r.status >= 200 && r.status <= 300)
                            ||
                            r.status == 304
                            ||
                            navigator.userAgent.indexOf("Safari") >= 0
                            && typeof r.status == "undefined";
                } catch (e) {
                }
                return false;
            }

            function httpData(r) {
                var ct = r.getResponseHeader("Content-Type") || r.getResponseHeader("content-type");
                if(ct) {
                    if(ct.indexOf("script") !== -1) {
                        eval.call(window, r.responseText);
                    }
                    if(ct.indexOf("xml") !== -1) {
                        return r.responseXML;
                    }
                    if(ct.indexOf("json") !== -1) {
                        try {
                            return JSON.parse(r.responseText);
                        } catch(e) {
                            console.error('JSON解析失败:', e, 'responseText:', r.responseText);
                            return r.responseText;
                        }
                    }
                }
                return r.responseText;
            }
        }

        // 正确的脱敏算法实现
        function maskName(name) {
            if (!name || name.length === 0) return '';
            if (name.length === 1) return '*';

            // 检测复姓
            var compoundSurnames = ['司马', '欧阳', '上官', '诸葛', '东方', '西门', '南宫', '轩辕'];
            for (var i = 0; i < compoundSurnames.length; i++) {
                if (name.indexOf(compoundSurnames[i]) === 0) {
                    return '**' + name.substring(2);
                }
            }

            // 检测少数民族长姓氏
            var minorityPrefixes = [
                {prefix: '阿布都拉', maskLength: 4},
                {prefix: '买买提', maskLength: 3},
                {prefix: '阿卜杜拉', maskLength: 4},
                {prefix: '穆罕默德', maskLength: 4},
                {prefix: '阿不都热', maskLength: 4},
                {prefix: '阿布都热', maskLength: 4}
            ];

            for (var j = 0; j < minorityPrefixes.length; j++) {
                var item = minorityPrefixes[j];
                if (name.indexOf(item.prefix) === 0) {
                    var stars = '';
                    for (var k = 0; k < item.maskLength; k++) {
                        stars += '*';
                    }
                    return stars + name.substring(item.maskLength);
                }
            }

            // 检测其他可能的长姓氏模式
            if (name.length >= 6) {
                if (name.indexOf('阿') === 0 || name.indexOf('买') === 0) {
                    var maskLength = Math.min(4, name.length - 2);
                    var stars = '';
                    for (var m = 0; m < maskLength; m++) {
                        stars += '*';
                    }
                    return stars + name.substring(maskLength);
                }
            }

            // 默认单字姓氏脱敏
            return '*' + name.substring(1);
        }

        function maskIdNumber(idNumber) {
            if (!idNumber || idNumber.length !== 18) return idNumber;
            return idNumber.substring(0, 6) + '********' + idNumber.substring(14);
        }



        // 读取身份证
        function readCard() {
            // 禁用按钮防止重复点击
            setButtonsEnabled(false);

            updateReaderStatus('reading');
            showLoading(true);
            updateProgress(10);

            var startTime = new Date().getTime();

            function onSuccess(data) {
                var endTime = new Date().getTime();
                var elapsed = endTime - startTime;

                showLoading(false);
                updateProgress(100);
                updateReaderStatus('ready');
                setButtonsEnabled(true); // 重新启用按钮

                try {
                    // 解析返回数据
                    var errorMsg = data.match(/"errorMsg"\s*:\s*"([^"]*)"/) ? data.match(/"errorMsg"\s*:\s*"([^"]*)"/) [1] : '';
                    var resultFlag = data.match(/"resultFlag"\s*:\s*"([^"]*)"/) ? data.match(/"resultFlag"\s*:\s*"([^"]*)"/) [1] : '';

                    if (resultFlag === '1' || errorMsg.includes('成功')) {
                        // 读取成功，解析数据
                        var name = data.match(/"partyName"\s*:\s*"([^"]*)"/) ? data.match(/"partyName"\s*:\s*"([^"]*)"/) [1] : '';
                        var gender = data.match(/"gender"\s*:\s*"([^"]*)"/) ? data.match(/"gender"\s*:\s*"([^"]*)"/) [1] : '';
                        var nation = data.match(/"nation"\s*:\s*"([^"]*)"/) ? data.match(/"nation"\s*:\s*"([^"]*)"/) [1] : '';
                        var birthday = data.match(/"bornDay"\s*:\s*"([^"]*)"/) ? data.match(/"bornDay"\s*:\s*"([^"]*)"/) [1] : '';
                        var idNumber = data.match(/"certNumber"\s*:\s*"([^"]*)"/) ? data.match(/"certNumber"\s*:\s*"([^"]*)"/) [1] : '';
                        var address = data.match(/"certAddress"\s*:\s*"([^"]*)"/) ? data.match(/"certAddress"\s*:\s*"([^"]*)"/) [1] : '';
                        var issueOrg = data.match(/"certOrg"\s*:\s*"([^"]*)"/) ? data.match(/"certOrg"\s*:\s*"([^"]*)"/) [1] : '';
                        var effDate = data.match(/"effDate"\s*:\s*"([^"]*)"/) ? data.match(/"effDate"\s*:\s*"([^"]*)"/) [1] : '';
                        var expDate = data.match(/"expDate"\s*:\s*"([^"]*)"/) ? data.match(/"expDate"\s*:\s*"([^"]*)"/) [1] : '';
                        var photo = data.match(/"identityPic"\s*:\s*"([^"]*)"/) ? data.match(/"identityPic"\s*:\s*"([^"]*)"/) [1] : '';

                        // 填充表单
                        document.getElementById('readTime').value = elapsed + ' 毫秒';
                        document.getElementById('certType').value = '居民身份证';
                        document.getElementById('name').textContent = name || '-';
                        document.getElementById('gender').textContent = gender === '1' ? '男' : gender === '2' ? '女' : gender;
                        document.getElementById('nation').textContent = nation || '-';
                        document.getElementById('birthday').textContent = formatDate(birthday) || '-';
                        document.getElementById('idNumber').value = idNumber || '';
                        document.getElementById('address').value = address || '';
                        document.getElementById('issueOrg').textContent = issueOrg || '-';
                        document.getElementById('validPeriod').textContent = formatDateRange(effDate, expDate);

                        // 显示照片
                        if (photo && photo.length > 10) {
                            document.getElementById('photoDisplay').innerHTML = '<img src="data:image/jpeg;base64,' + photo + '" alt="身份证照片">';
                        }





                        // 显示结果
                        var result = '✅ 身份证读取成功！\n\n';
                        result += '📋 读取信息：\n';
                        result += '姓名：' + name + '\n';
                        result += '身份证号：' + idNumber + '\n';
                        result += '性别：' + (gender === '1' ? '男' : gender === '2' ? '女' : gender) + '\n';
                        result += '民族：' + nation + '\n';
                        result += '出生日期：' + formatDate(birthday) + '\n\n';
                        result += '⏱️ 读取耗时：' + elapsed + ' 毫秒\n';
                        result += '✅ 身份证读取完成！';

                        document.getElementById('resultArea').textContent = result;
                        document.getElementById('resultArea').className = 'result-area result-success';

                    } else {
                        throw new Error(errorMsg || '读取失败');
                    }

                } catch(e) {
                    updateReaderStatus('error');
                    document.getElementById('resultArea').textContent = '❌ 身份证读取失败：' + e.message + '\n\n请检查：\n• 身份证是否正确放置\n• 读卡器是否正常连接\n• 驱动程序是否安装';
                    document.getElementById('resultArea').className = 'result-area result-error';
                }

                setTimeout(() => updateProgress(0), 2000);
            }

            function onError() {
                // 如果官方中间件失败，尝试使用备用模拟服务
                console.log('官方中间件连接失败，尝试使用备用模拟服务...');

                var backupOptions = {
                    type: "GET",
                    url: "http://*********:8080/readcard?t=" + Math.random(),
                    timeout: 5000,
                    onSuccess: function(data) {
                        console.log('备用模拟服务连接成功');
                        onSuccess(data);
                    },
                    onError: function() {
                        showLoading(false);
                        updateProgress(0);
                        updateReaderStatus('error');
                        setButtonsEnabled(true); // 重新启用按钮

                        var errorMsg = '❌ 身份证读取失败\n\n';
                        errorMsg += '请检查以下项目：\n';
                        errorMsg += '• CVR100U读卡器是否正确连接\n';
                        errorMsg += '• 官方中间件是否在 http://*********:19196 运行\n';
                        errorMsg += '• 身份证是否正确放置在读卡器上\n';
                        errorMsg += '• 驱动程序是否正确安装\n\n';
                        errorMsg += '💡 提示：\n';
                        errorMsg += '• 可以先访问 http://*********:19196/mainpage 测试官方页面\n';
                        errorMsg += '• 确认官方页面能正常读卡后再使用本系统\n';
                        errorMsg += '• 或点击"查看样本"和"测试脱敏"体验其他功能';

                        document.getElementById('resultArea').textContent = errorMsg;
                        document.getElementById('resultArea').className = 'result-area result-error';
                    }
                };

                ajax(backupOptions);
            }

            // 发起读卡请求 - 调用官方中间件
            var options = {
                type: "GET",
                url: "http://*********:19196/readcard?t=" + Math.random(),
                timeout: 10000,
                onSuccess: onSuccess,
                onError: onError
            };

            ajax(options);
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr || dateStr.length !== 8) return dateStr;
            return dateStr.substring(0, 4) + '-' + dateStr.substring(4, 6) + '-' + dateStr.substring(6, 8);
        }

        // 格式化日期范围
        function formatDateRange(startDate, endDate) {
            if (!startDate && !endDate) return '-';
            var start = formatDate(startDate) || '';
            var end = formatDate(endDate) || '';
            if (end === '长期') return start + ' 至 长期';
            return start + ' 至 ' + end;
        }

        // 自动比对
        function autoCompare() {
            console.log('=== autoCompare函数被调用 ===');

            // 添加视觉反馈
            var btn = document.getElementById('autoCompareBtn');
            if (btn) {
                btn.style.backgroundColor = '#e74c3c';
                btn.textContent = '🔄 比对中...';
                btn.disabled = true;
            }

            var maskedName = document.getElementById('maskedName').value;
            var maskedId = document.getElementById('maskedId').value;

            console.log('脱敏姓名:', maskedName);
            console.log('脱敏身份证:', maskedId);

            if (!maskedName || !maskedId) {
                console.log('缺少脱敏数据，显示警告');
                document.getElementById('resultArea').textContent = '⚠️ 请先读取身份证信息或点击"填充测试数据"';
                document.getElementById('resultArea').className = 'result-area result-warning';

                // 恢复按钮状态
                if (btn) {
                    btn.style.backgroundColor = '';
                    btn.textContent = '🔍 自动比对';
                    btn.disabled = false;
                }
                return;
            }

            console.log('开始调用searchDatabase');
            searchDatabase();
        }

        // 查询数据库
        function searchDatabase() {
            console.log('searchDatabase函数被调用');
            var maskedName = document.getElementById('maskedName').value;
            var maskedId = document.getElementById('maskedId').value;

            console.log('查询参数 - 姓名:', maskedName, '身份证:', maskedId);

            if (!maskedName || !maskedId) {
                console.log('查询参数不完整');
                document.getElementById('resultArea').textContent = '⚠️ 请输入脱敏后的姓名和身份证号';
                document.getElementById('resultArea').className = 'result-area result-warning';
                return;
            }

            console.log('更新比对状态为comparing');
            updateCompareStatus('comparing');

            var requestUrl = "http://*********:8080/api/search?name=" + encodeURIComponent(maskedName) + "&id=" + encodeURIComponent(maskedId) + "&t=" + Math.random();
            console.log('准备发送请求到:', requestUrl);

            var options = {
                type: "GET",
                url: requestUrl,
                timeout: 5000,
                onSuccess: function(data) {
                    console.log('搜索API返回数据:', data);
                    console.log('数据类型:', typeof data);
                    updateCompareStatus('ready');

                    // 恢复按钮状态
                    var btn = document.getElementById('autoCompareBtn');
                    if (btn) {
                        btn.style.backgroundColor = '';
                        btn.textContent = '🔍 自动比对';
                        btn.disabled = false;
                    }

                    handleSearchResult(data, maskedName, maskedId);
                },
                onError: function() {
                    console.error('搜索API请求失败');
                    console.error('请求URL:', requestUrl);
                    updateCompareStatus('error');

                    // 恢复按钮状态
                    var btn = document.getElementById('autoCompareBtn');
                    if (btn) {
                        btn.style.backgroundColor = '';
                        btn.textContent = '🔍 自动比对';
                        btn.disabled = false;
                    }

                    document.getElementById('resultArea').textContent = '❌ 数据库查询失败\n\n请检查：\n• API服务器是否启动 (http://*********:8080)\n• 数据库连接是否正常\n• 网络连接是否稳定';
                    document.getElementById('resultArea').className = 'result-area result-error';
                }
            };

            console.log('发送AJAX请求');
            ajax(options);
        }

        // 处理搜索结果
        function handleSearchResult(data, maskedName, maskedId) {
            try {
                console.log('处理搜索结果，原始数据:', data);

                // 检查数据是否为空或无效
                if (!data || data.trim() === '') {
                    throw new Error('API返回空数据');
                }

                var result;
                if (typeof data === 'string') {
                    result = JSON.parse(data);
                } else {
                    result = data;
                }

                console.log('解析后的结果:', result);

                var comparisonDiv = document.getElementById('comparisonResult');
                var matchDiv = document.getElementById('matchResult');

                if (result.found) {
                    // 找到匹配记录

                    comparisonDiv.className = 'comparison-result match-success';
                    comparisonDiv.style.display = 'block';

                    matchDiv.innerHTML = `
                        <div style="display: flex; align-items: center; margin-bottom: 15px;">
                            <span class="status-indicator status-success"></span>
                            <strong style="color: #27ae60; font-size: 1.1em;">✅ 匹配成功</strong>
                        </div>
                        <div style="background: rgba(255,255,255,0.8); padding: 15px; border-radius: 8px;">
                            <div style="margin-bottom: 10px;"><strong>数据库记录：</strong></div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                                <div>姓名：${result.record.name}</div>
                                <div>身份证：${result.record.id_number}</div>
                                <div>准考车型：${result.record.allowed_car_type || '-'}</div>
                                <div>考试日期：${result.record.exam_date || '-'}</div>
                                <div>考试场地：${result.record.exam_venue || '-'}</div>
                                <div>记录ID：${result.record.id}</div>
                            </div>
                        </div>
                    `;

                    var resultText = '🎉 数据库比对成功！\n\n';
                    resultText += '🔍 查询条件：\n';
                    resultText += '脱敏姓名：' + maskedName + '\n';
                    resultText += '脱敏身份证：' + maskedId + '\n\n';
                    resultText += '✅ 匹配结果：\n';
                    resultText += '数据库姓名：' + result.record.name + '\n';
                    resultText += '数据库身份证：' + result.record.id_number + '\n';
                    resultText += '准考车型：' + (result.record.allowed_car_type || '-') + '\n';
                    resultText += '考试日期：' + (result.record.exam_date || '-') + '\n';
                    resultText += '考试场地：' + (result.record.exam_venue || '-') + '\n\n';
                    resultText += '🎯 验证通过：身份信息与数据库记录完全匹配';

                    document.getElementById('resultArea').textContent = resultText;
                    document.getElementById('resultArea').className = 'result-area result-success';

                } else {
                    // 未找到匹配记录

                    comparisonDiv.className = 'comparison-result match-failed';
                    comparisonDiv.style.display = 'block';

                    matchDiv.innerHTML = `
                        <div style="display: flex; align-items: center; margin-bottom: 15px;">
                            <span class="status-indicator status-error"></span>
                            <strong style="color: #e74c3c; font-size: 1.1em;">❌ 未找到匹配</strong>
                        </div>
                        <div style="background: rgba(255,255,255,0.8); padding: 15px; border-radius: 8px;">
                            <div style="margin-bottom: 10px;"><strong>查询条件：</strong></div>
                            <div style="font-size: 14px;">
                                <div>脱敏姓名：${maskedName}</div>
                                <div>脱敏身份证：${maskedId}</div>
                            </div>
                            <div style="margin-top: 10px; color: #666; font-size: 13px;">
                                可能原因：数据库中无此记录，或脱敏算法不匹配
                            </div>
                        </div>
                    `;

                    var resultText = '⚠️ 数据库中未找到匹配记录\n\n';
                    resultText += '🔍 查询条件：\n';
                    resultText += '脱敏姓名：' + maskedName + '\n';
                    resultText += '脱敏身份证：' + maskedId + '\n\n';
                    resultText += '❌ 可能原因：\n';
                    resultText += '• 数据库中确实没有此人记录\n';
                    resultText += '• 脱敏算法与数据库不匹配\n';
                    resultText += '• 输入的信息有误\n\n';
                    resultText += '💡 建议：\n';
                    resultText += '• 检查输入信息是否正确\n';
                    resultText += '• 查看数据库样本进行对比\n';
                    resultText += '• 联系系统管理员确认';

                    document.getElementById('resultArea').textContent = resultText;
                    document.getElementById('resultArea').className = 'result-area result-warning';
                }

            } catch(e) {
                console.error('搜索结果处理错误:', e);
                console.error('原始数据:', data);
                updateCompareStatus('error');

                var errorMsg = '❌ 数据解析失败：' + e.message + '\n\n';
                errorMsg += '🔍 调试信息：\n';
                errorMsg += '• 查询姓名：' + maskedName + '\n';
                errorMsg += '• 查询身份证：' + maskedId + '\n';
                errorMsg += '• 原始响应：' + (data ? data.substring(0, 100) + '...' : '空数据') + '\n\n';
                errorMsg += '💡 可能的解决方案：\n';
                errorMsg += '• 检查API服务器是否正常运行\n';
                errorMsg += '• 检查网络连接状态\n';
                errorMsg += '• 重新放置身份证重试';

                document.getElementById('resultArea').textContent = errorMsg;
                document.getElementById('resultArea').className = 'result-area result-error';
            }
        }

        // 查看样本数据
        function viewSamples() {
            var options = {
                type: "GET",
                url: "http://*********:8080/api/sample?t=" + Math.random(),
                timeout: 5000,
                onSuccess: function(data) {
                    console.log('API返回数据:', data);
                    try {
                        // 检查数据是否为空或无效
                        if (!data || data.trim() === '') {
                            throw new Error('API返回空数据');
                        }

                        var result;
                        if (typeof data === 'string') {
                            result = JSON.parse(data);
                        } else {
                            result = data;
                        }

                        // 检查返回的数据结构
                        if (result.samples && Array.isArray(result.samples)) {
                            displaySamples(result.samples);
                        } else if (Array.isArray(result)) {
                            displaySamples(result);
                        } else {
                            throw new Error('数据格式不正确');
                        }
                    } catch(e) {
                        console.error('数据解析错误:', e);
                        document.getElementById('resultArea').textContent = '❌ 数据解析失败：' + e.message + '\n\n正在使用模拟数据...';
                        document.getElementById('resultArea').className = 'result-area result-warning';
                        setTimeout(displayMockSamples, 1000);
                    }
                },
                onError: function() {
                    console.log('API请求失败，使用模拟数据');
                    document.getElementById('resultArea').textContent = '⚠️ API连接失败，正在使用模拟数据...';
                    document.getElementById('resultArea').className = 'result-area result-warning';
                    setTimeout(displayMockSamples, 1000);
                }
            };

            ajax(options);
        }

        // 显示样本数据
        function displaySamples(samples) {
            var resultText = '📋 数据库脱敏样本数据\n\n';
            resultText += '序号  脱敏姓名    脱敏身份证号           准考车型\n';
            resultText += '─'.repeat(50) + '\n';

            for (var i = 0; i < Math.min(samples.length, 15); i++) {
                var sample = samples[i];
                var index = (i + 1).toString().padStart(2, ' ');
                var name = (sample.name || '').padEnd(8, ' ');
                var idNumber = (sample.id_number || '').padEnd(20, ' ');
                var carType = sample.allowed_car_type || '-';

                resultText += index + '   ' + name + ' ' + idNumber + ' ' + carType + '\n';
            }

            resultText += '\n💡 使用说明：\n';
            resultText += '• 单*脱敏：汉族单字姓氏 (如 *文华)\n';
            resultText += '• 双*脱敏：复姓 (如 **政德)\n';
            resultText += '• 多*脱敏：少数民族长姓氏 (如 ****吾斯曼)\n';
            resultText += '• 总数据量：100,547条记录\n';
            resultText += '• 脱敏算法基于真实数据分析';

            document.getElementById('resultArea').textContent = resultText;
            document.getElementById('resultArea').className = 'result-area';
        }

        // 显示模拟样本
        function displayMockSamples() {
            var mockSamples = [
                {name: '*慧', id_number: '440921********3602', allowed_car_type: 'C1'},
                {name: '*肖丹', id_number: '440921********042X', allowed_car_type: 'C2'},
                {name: '*振威', id_number: '440983********0412', allowed_car_type: 'C1'},
                {name: '**霖', id_number: '440902********3619', allowed_car_type: 'C1'},
                {name: '**政德', id_number: '350305********1139', allowed_car_type: 'C1'}
            ];

            displaySamples(mockSamples);
        }

        // 测试脱敏算法
        function testMasking() {
            var testCases = [
                {original: '张三', type: '单字姓氏'},
                {original: '李四', type: '单字姓氏'},
                {original: '王五六', type: '单字姓氏'},
                {original: '司马懿', type: '复姓'},
                {original: '欧阳修', type: '复姓'},
                {original: '诸葛亮', type: '复姓'},
                {original: '阿布都拉吾斯曼', type: '少数民族'},
                {original: '买买提热合曼', type: '少数民族'}
            ];

            var resultText = '🧪 脱敏算法测试结果\n\n';
            resultText += '原始姓名        脱敏结果        类型\n';
            resultText += '─'.repeat(40) + '\n';

            for (var i = 0; i < testCases.length; i++) {
                var testCase = testCases[i];
                var masked = maskName(testCase.original);
                var original = testCase.original.padEnd(12, ' ');
                var maskedResult = masked.padEnd(12, ' ');

                resultText += original + ' ' + maskedResult + ' ' + testCase.type + '\n';
            }

            resultText += '\n📊 算法统计：\n';
            resultText += '• 基于100,547条真实数据分析\n';
            resultText += '• 单*脱敏：94.05% (汉族单字姓氏)\n';
            resultText += '• 双*脱敏：5.89% (复姓)\n';
            resultText += '• 多*脱敏：0.06% (少数民族长姓氏)\n';
            resultText += '• 算法准确率：99.94%';

            document.getElementById('resultArea').textContent = resultText;
            document.getElementById('resultArea').className = 'result-area';
        }



        // 填充测试数据
        function fillTestData() {
            console.log('填充测试数据');

            // 使用API返回的第一个样本数据
            document.getElementById('maskedName').value = '*光辉';
            document.getElementById('maskedId').value = '440921********7435';

            // 更新结果区域
            document.getElementById('resultArea').textContent = '✅ 测试数据已填充\n\n脱敏姓名：*光辉\n脱敏身份证：440921********7435\n\n🎯 现在可以点击"自动比对"进行测试';
            document.getElementById('resultArea').className = 'result-area result-success';

            console.log('测试数据填充完成');
        }

        // 测试JavaScript功能
        function testFunction() {
            console.log('=== JavaScript测试开始 ===');
            alert('JavaScript功能正常！\n\n请查看控制台日志获取详细信息。');

            // 测试DOM操作
            var resultArea = document.getElementById('resultArea');
            if (resultArea) {
                console.log('✅ resultArea元素找到');
            } else {
                console.log('❌ resultArea元素未找到');
            }

            // 测试脱敏数据输入框
            var maskedName = document.getElementById('maskedName');
            var maskedId = document.getElementById('maskedId');

            if (maskedName && maskedId) {
                console.log('✅ 脱敏数据输入框找到');
                console.log('当前脱敏姓名:', maskedName.value);
                console.log('当前脱敏身份证:', maskedId.value);
            } else {
                console.log('❌ 脱敏数据输入框未找到');
            }

            // 测试autoCompare函数
            if (typeof autoCompare === 'function') {
                console.log('✅ autoCompare函数存在');
            } else {
                console.log('❌ autoCompare函数不存在');
            }

            console.log('=== JavaScript测试完成 ===');
        }

        // 测试API连接
        function testAPI() {
            console.log('=== API连接测试开始 ===');

            // 测试样本API
            var sampleUrl = 'http://*********:8080/api/sample?t=' + Math.random();
            console.log('测试样本API:', sampleUrl);

            var options = {
                type: "GET",
                url: sampleUrl,
                timeout: 5000,
                onSuccess: function(data) {
                    console.log('✅ 样本API测试成功');
                    console.log('返回数据:', data);

                    // 测试搜索API
                    var searchUrl = 'http://*********:8080/api/search?name=*光辉&id=440921********7435&t=' + Math.random();
                    console.log('测试搜索API:', searchUrl);

                    var searchOptions = {
                        type: "GET",
                        url: searchUrl,
                        timeout: 5000,
                        onSuccess: function(searchData) {
                            console.log('✅ 搜索API测试成功');
                            console.log('搜索结果:', searchData);
                            alert('API测试成功！\n\n样本API: ✅\n搜索API: ✅\n\n请查看控制台获取详细信息。');
                        },
                        onError: function() {
                            console.log('❌ 搜索API测试失败');
                            alert('API测试部分成功\n\n样本API: ✅\n搜索API: ❌\n\n请查看控制台获取详细信息。');
                        }
                    };
                    ajax(searchOptions);
                },
                onError: function() {
                    console.log('❌ 样本API测试失败');
                    alert('API测试失败\n\n样本API: ❌\n\n请确认API服务器是否运行在 http://*********:8080');
                }
            };

            ajax(options);
        }



        // 更新自动读取状态显示
        function updateAutoReadStatus(status, text, hint) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            const autoReadHint = document.querySelector('.auto-read-hint');
            const autoReadStatus = document.getElementById('autoReadStatus');

            // 移除所有状态类
            statusDot.className = 'status-dot';
            statusText.className = 'status-text';
            autoReadStatus.className = 'auto-read-status';

            // 添加新状态类
            statusDot.classList.add(status);
            if (status === 'success') {
                statusText.classList.add('success');
                autoReadStatus.classList.add('success');
            }

            statusText.textContent = text;
            if (hint) {
                autoReadHint.textContent = hint;
            }
        }

        // 启动自动检测
        function startAutoDetection() {
            console.log('启动自动检测模式');
            updateAutoReadStatus('waiting', '等待放置证件...', '请将身份证放置在读卡器上');

            if (autoReadInterval) {
                clearInterval(autoReadInterval);
            }

            autoReadInterval = setInterval(function() {
                if (autoReadMode) {
                    detectCard();
                }
            }, 2000); // 每2秒检测一次，减少频率
        }

        // 停止自动检测
        function stopAutoDetection() {
            console.log('停止自动检测模式');
            if (autoReadInterval) {
                clearInterval(autoReadInterval);
                autoReadInterval = null;
            }
            updateAutoReadStatus('waiting', '检测已停止', '自动检测功能已关闭');
        }

        // 检测证件是否在位
        function detectCard() {
            // 如果正在读取中，跳过检测
            if (document.getElementById('loadingIndicator').style.display === 'block') {
                return;
            }

            // 记录检测开始时间
            detectionStartTime = Date.now();

            // 通过尝试读取来检测证件是否在位
            var options = {
                type: "GET",
                url: "http://*********:19196/readcard?t=" + Math.random(),
                timeout: 2000, // 缩短超时时间
                onSuccess: function(data) {
                    handleCardDetection(data, true);
                },
                onError: function() {
                    // 如果官方中间件失败，尝试备用服务
                    var backupOptions = {
                        type: "GET",
                        url: "http://*********:8080/readcard?t=" + Math.random(),
                        timeout: 2000,
                        onSuccess: function(data) {
                            handleCardDetection(data, true);
                        },
                        onError: function() {
                            handleCardDetection(null, false);
                        }
                    };
                    ajax(backupOptions);
                }
            };

            ajax(options);
        }

        // 处理证件检测结果
        function handleCardDetection(data, success) {
            const currentTime = Date.now();

            if (success && data) {
                // 检查是否是有效的身份证数据
                var errorMsg = data.match(/"errorMsg"\s*:\s*"([^"]*)"/) ? data.match(/"errorMsg"\s*:\s*"([^"]*)"/) [1] : '';
                var resultFlag = data.match(/"resultFlag"\s*:\s*"([^"]*)"/) ? data.match(/"resultFlag"\s*:\s*"([^"]*)"/) [1] : '';

                // 尝试解析关键字段来判断是否成功
                var name = data.match(/"partyName"\s*:\s*"([^"]*)"/) ? data.match(/"partyName"\s*:\s*"([^"]*)"/) [1] : '';
                var idNumber = data.match(/"certNumber"\s*:\s*"([^"]*)"/) ? data.match(/"certNumber"\s*:\s*"([^"]*)"/) [1] : '';

                console.log('检测阶段数据解析 - errorMsg:', errorMsg, 'resultFlag:', resultFlag, '姓名:', name, '身份证:', idNumber);

                if ((resultFlag === '1' || errorMsg.includes('成功')) || (name && idNumber)) {
                    // 检测到有效证件
                    cardDetectionCount++;

                    if (!isCardPresent) {
                        // 证件刚放置
                        console.log('检测到证件放置');
                        isCardPresent = true;
                        updateAutoReadStatus('detecting', '检测到证件', '正在验证证件信息...');

                        // 延迟一下再读取，确保证件放置稳定
                        setTimeout(function() {
                            if (isCardPresent && autoReadMode) {
                                performAutoRead(data);
                            }
                        }, 500);
                    } else {
                        // 证件一直在位，检查是否需要重新读取
                        if (currentTime - lastReadTime > 30000) { // 30秒后允许重新读取
                            console.log('证件长时间在位，允许重新读取');
                            performAutoRead(data);
                        }
                    }
                } else {
                    // 没有检测到有效证件
                    handleCardRemoval();
                }
            } else {
                // 检测失败，可能证件不在位
                handleCardRemoval();
            }
        }

        // 处理证件移除
        function handleCardRemoval() {
            cardDetectionCount = 0;
            detectionStartTime = 0; // 重置检测开始时间
            if (isCardPresent) {
                console.log('检测到证件移除');
                isCardPresent = false;
                updateAutoReadStatus('waiting', '等待放置证件...', '请将身份证放置在读卡器上');
                updateReaderStatus('ready'); // 重置读卡器状态
            }
        }

        // 执行自动读取
        function performAutoRead(data) {
            console.log('执行自动读取');
            var currentTime = Date.now();
            lastReadTime = currentTime;
            updateAutoReadStatus('reading', '正在读取...', '请保持证件稳定');

            // 计算从检测开始到处理完成的真实耗时
            var elapsed = detectionStartTime > 0 ? (currentTime - detectionStartTime) : 0;
            console.log('自动读取耗时计算: 开始时间=' + detectionStartTime + ', 结束时间=' + currentTime + ', 耗时=' + elapsed + 'ms');

            try {
                // 解析返回数据
                var errorMsg = data.match(/"errorMsg"\s*:\s*"([^"]*)"/) ? data.match(/"errorMsg"\s*:\s*"([^"]*)"/) [1] : '';
                var resultFlag = data.match(/"resultFlag"\s*:\s*"([^"]*)"/) ? data.match(/"resultFlag"\s*:\s*"([^"]*)"/) [1] : '';

                console.log('自动读取数据解析 - errorMsg:', errorMsg, 'resultFlag:', resultFlag);
                console.log('原始数据片段:', data.substring(0, 200));

                // 尝试解析关键字段来判断是否成功
                var name = data.match(/"partyName"\s*:\s*"([^"]*)"/) ? data.match(/"partyName"\s*:\s*"([^"]*)"/) [1] : '';
                var idNumber = data.match(/"certNumber"\s*:\s*"([^"]*)"/) ? data.match(/"certNumber"\s*:\s*"([^"]*)"/) [1] : '';

                // 更宽松的成功判断：如果有姓名和身份证号就认为成功
                if ((resultFlag === '1' || errorMsg.includes('成功')) || (name && idNumber)) {
                    console.log('自动读取判断为成功 - 姓名:', name, '身份证:', idNumber);

                    // 读取成功，解析其他数据
                    var gender = data.match(/"gender"\s*:\s*"([^"]*)"/) ? data.match(/"gender"\s*:\s*"([^"]*)"/) [1] : '';
                    var nation = data.match(/"nation"\s*:\s*"([^"]*)"/) ? data.match(/"nation"\s*:\s*"([^"]*)"/) [1] : '';
                    var birthday = data.match(/"bornDay"\s*:\s*"([^"]*)"/) ? data.match(/"bornDay"\s*:\s*"([^"]*)"/) [1] : '';
                    var address = data.match(/"certAddress"\s*:\s*"([^"]*)"/) ? data.match(/"certAddress"\s*:\s*"([^"]*)"/) [1] : '';
                    var issueOrg = data.match(/"certOrg"\s*:\s*"([^"]*)"/) ? data.match(/"certOrg"\s*:\s*"([^"]*)"/) [1] : '';
                    var effDate = data.match(/"effDate"\s*:\s*"([^"]*)"/) ? data.match(/"effDate"\s*:\s*"([^"]*)"/) [1] : '';
                    var expDate = data.match(/"expDate"\s*:\s*"([^"]*)"/) ? data.match(/"expDate"\s*:\s*"([^"]*)"/) [1] : '';
                    var photo = data.match(/"identityPic"\s*:\s*"([^"]*)"/) ? data.match(/"identityPic"\s*:\s*"([^"]*)"/) [1] : '';

                    // 重新计算最终耗时（包含数据处理时间）
                    var finalElapsed = detectionStartTime > 0 ? (Date.now() - detectionStartTime) : elapsed;
                    console.log('最终耗时计算: ' + finalElapsed + 'ms');

                    // 填充表单
                    document.getElementById('readTime').value = finalElapsed + ' 毫秒';
                    document.getElementById('certType').value = '居民身份证';
                    document.getElementById('name').textContent = name || '-';
                    document.getElementById('gender').textContent = gender === '1' ? '男' : gender === '2' ? '女' : gender;
                    document.getElementById('nation').textContent = nation || '-';
                    document.getElementById('birthday').textContent = formatDate(birthday) || '-';
                    document.getElementById('idNumber').value = idNumber || '';
                    document.getElementById('address').value = address || '';
                    document.getElementById('issueOrg').textContent = issueOrg || '-';
                    document.getElementById('validPeriod').textContent = formatDateRange(effDate, expDate);

                    // 显示照片
                    if (photo && photo.length > 10) {
                        document.getElementById('photoDisplay').innerHTML = '<img src="data:image/jpeg;base64,' + photo + '" alt="身份证照片">';
                    }

                    // 显示结果
                    var result = '🎉 自动读取完成！\n\n';
                    result += '📋 身份证信息：\n';
                    result += '姓名：' + name + '\n';
                    result += '身份证号：' + idNumber + '\n';
                    result += '性别：' + (gender === '1' ? '男' : gender === '2' ? '女' : gender) + '\n';
                    result += '民族：' + nation + '\n';
                    result += '出生日期：' + formatDate(birthday) + '\n\n';
                    result += '⏱️ 读取耗时：' + finalElapsed + ' 毫秒\n';
                    result += '✅ 状态：读取完成\n';
                    result += '💡 提示：证件保持在位不会重复读取，移除证件可进行下次读取';

                    document.getElementById('resultArea').textContent = result;
                    document.getElementById('resultArea').className = 'result-area result-success';

                    updateAutoReadStatus('success', '读取完成', '证件信息已获取，可移除证件');
                    updateReaderStatus('completed');

                } else {
                    throw new Error(errorMsg || '读取失败');
                }

            } catch(e) {
                console.error('自动读取异常:', e);

                // 检查表单是否已经填充了数据，如果有数据就认为是成功的
                var formName = document.getElementById('name').textContent;
                var formIdNumber = document.getElementById('idNumber').value;

                if (formName && formName !== '-' && formIdNumber) {
                    console.log('虽然出现异常，但表单已有数据，认为读取成功');
                    updateAutoReadStatus('success', '读取完成', '证件信息已获取，可移除证件');
                    updateReaderStatus('completed');

                    // 显示成功结果
                    var result = '🎉 自动读取完成！\n\n';
                    result += '📋 身份证信息：\n';
                    result += '姓名：' + formName + '\n';
                    result += '身份证号：' + formIdNumber + '\n\n';
                    result += '✅ 状态：读取完成\n';
                    result += '💡 提示：证件保持在位不会重复读取，移除证件可进行下次读取';

                    document.getElementById('resultArea').textContent = result;
                    document.getElementById('resultArea').className = 'result-area result-success';
                } else {
                    console.log('读取确实失败，表单无数据');
                    updateAutoReadStatus('error', '读取失败', '请重新放置证件');
                    updateReaderStatus('error');

                    document.getElementById('resultArea').textContent = '❌ 自动读取失败：' + e.message + '\n\n请重新放置身份证';
                    document.getElementById('resultArea').className = 'result-area result-error';

                    // 重置状态，允许重新检测
                    isCardPresent = false;
                    setTimeout(function() {
                        if (autoReadMode) {
                            updateAutoReadStatus('waiting', '等待放置证件...', '请将身份证放置在读卡器上');
                        }
                    }, 3000);
                }
            }
        }


    </script>
</body>
</html>
