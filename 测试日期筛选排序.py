#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期筛选排序功能
验证"数据记录"页面中日期筛选组件的倒序排序是否正确工作
"""

import sqlite3
import os
from datetime import datetime

def test_date_sorting_logic():
    """测试日期排序逻辑"""
    print("🧪 测试日期排序逻辑...")
    
    # 模拟不同的日期数据
    test_dates = ["8/1", "8/2", "8/10", "7/30", "9/1", "8/15", "7/25"]
    
    def sort_date_key(date_str):
        """将日期字符串转换为可排序的格式"""
        try:
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) == 2:
                    month, day = int(parts[0]), int(parts[1])
                    return (month, day)
            return (0, 0)  # 无效日期排在最后
        except:
            return (0, 0)
    
    # 测试正序排列
    sorted_asc = sorted(test_dates, key=sort_date_key)
    print(f"正序排列: {sorted_asc}")
    
    # 测试倒序排列
    sorted_desc = sorted(test_dates, key=sort_date_key, reverse=True)
    print(f"倒序排列: {sorted_desc}")
    
    print("✅ 日期排序逻辑测试完成")

def get_actual_dates_from_db():
    """从数据库获取实际的日期数据"""
    db_path = "database/motosim.db"
    
    if not os.path.exists(db_path):
        return None, "数据库文件不存在"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='motosim_records'")
        if not cursor.fetchone():
            conn.close()
            return None, "表不存在"
        
        # 获取去重的日期数据
        cursor.execute("SELECT DISTINCT 模拟日期 FROM motosim_records WHERE 模拟日期 IS NOT NULL")
        dates_raw = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        return dates_raw, None
    except Exception as e:
        return None, str(e)

def test_database_date_sorting():
    """测试数据库中实际日期的排序"""
    print("\n📊 测试数据库日期排序...")
    
    dates_raw, error = get_actual_dates_from_db()
    
    if error:
        print(f"❌ 无法获取数据库日期: {error}")
        return
    
    if not dates_raw:
        print("⚠️  数据库中没有日期数据")
        return
    
    print(f"数据库中的原始日期: {dates_raw}")
    
    # 使用修复后的排序逻辑
    def sort_date_key(date_str):
        """将日期字符串转换为可排序的格式"""
        try:
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) == 2:
                    month, day = int(parts[0]), int(parts[1])
                    return (month, day)
            return (0, 0)  # 无效日期排在最后
        except:
            return (0, 0)
    
    # 按日期倒序排列（最新的在前）
    unique_dates = sorted(dates_raw, key=sort_date_key, reverse=True)
    
    print(f"倒序排列结果: {unique_dates}")
    print("✅ 数据库日期排序测试完成")

def check_app_modification():
    """检查app.py的修改是否正确"""
    print("\n🔍 检查app.py修改状态...")
    
    try:
        with open("app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修改标志
        checks = [
            ('日期倒序排列', '注释说明日期倒序'),
            ('def sort_date_key(date_str)', '智能日期排序函数'),
            ('sorted(dates_raw, key=sort_date_key, reverse=True)', '倒序排序调用'),
            ('WHERE 模拟日期 IS NOT NULL', '过滤空日期'),
        ]
        
        print("修改检查:")
        for check, description in checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少{description}")
        
        # 检查是否移除了旧的排序方法
        old_checks = [
            ('ORDER BY 模拟日期 DESC', '旧的SQL排序方法'),
        ]
        
        print("\n旧代码清理:")
        for check, description in old_checks:
            if check in content:
                print(f"⚠️  仍然包含{description}")
            else:
                print(f"✅ 已移除{description}")
                
        return True
    except Exception as e:
        print(f"❌ 无法读取app.py文件: {e}")
        return False

def test_sorting_edge_cases():
    """测试边界情况"""
    print("\n🎯 测试边界情况...")
    
    edge_cases = [
        "12/31",  # 年末
        "1/1",    # 年初
        "2/29",   # 闰年
        "invalid", # 无效格式
        "",       # 空字符串
        "13/1",   # 无效月份
        "12/32",  # 无效日期
    ]
    
    def sort_date_key(date_str):
        """将日期字符串转换为可排序的格式"""
        try:
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) == 2:
                    month, day = int(parts[0]), int(parts[1])
                    return (month, day)
            return (0, 0)  # 无效日期排在最后
        except:
            return (0, 0)
    
    print("边界情况测试:")
    for case in edge_cases:
        try:
            sort_key = sort_date_key(case)
            print(f"  '{case}' → {sort_key}")
        except Exception as e:
            print(f"  '{case}' → 错误: {e}")
    
    print("✅ 边界情况测试完成")

def main():
    """主函数"""
    print("📅 日期筛选排序功能测试")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试排序逻辑
    test_date_sorting_logic()
    
    # 测试数据库实际数据
    test_database_date_sorting()
    
    # 检查代码修改
    check_app_modification()
    
    # 测试边界情况
    test_sorting_edge_cases()
    
    print("\n💡 测试建议:")
    print("1. 重启Flask应用以应用修改:")
    print("   停止当前应用，重新运行 python3 app.py")
    print()
    print("2. 访问数据记录页面测试:")
    print("   访问 http://localhost:5001/records")
    print("   查看日期筛选下拉菜单中的日期顺序")
    print()
    print("3. 验证排序效果:")
    print("   - 最新的日期应该在下拉菜单顶部")
    print("   - 日期应该按月/日的逻辑倒序排列")
    print("   - 无效日期应该排在最后")
    
    print("\n" + "=" * 60)
    print("🎉 日期筛选排序功能测试完成！")

if __name__ == "__main__":
    main() 