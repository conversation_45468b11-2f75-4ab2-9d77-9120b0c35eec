#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摩托车模拟登记表管理系统 - 数据库清空脚本
功能：清空数据库所有记录并重置自增ID
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_database_stats(db_path):
    """获取数据库统计信息"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取总记录数
        cursor.execute("SELECT COUNT(*) FROM motosim_records")
        total_records = cursor.fetchone()[0]
        
        # 获取最新记录时间
        cursor.execute("SELECT MAX(创建时间) FROM motosim_records")
        latest_time = cursor.fetchone()[0]
        
        # 获取最早记录时间
        cursor.execute("SELECT MIN(创建时间) FROM motosim_records")
        earliest_time = cursor.fetchone()[0]
        
        # 获取当前自增ID值
        cursor.execute("SELECT seq FROM sqlite_sequence WHERE name='motosim_records'")
        current_id = cursor.fetchone()
        current_id = current_id[0] if current_id else 0
        
        conn.close()
        
        return {
            'total_records': total_records,
            'latest_time': latest_time,
            'earliest_time': earliest_time,
            'current_id': current_id
        }
    except Exception as e:
        return None

def backup_database(db_path):
    """创建数据库备份"""
    try:
        backup_filename = f"motosim_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join(os.path.dirname(db_path), backup_filename)
        
        # 使用SQLite的备份API
        source_conn = sqlite3.connect(db_path)
        backup_conn = sqlite3.connect(backup_path)
        source_conn.backup(backup_conn)
        source_conn.close()
        backup_conn.close()
        
        return backup_path
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None

def clear_database(db_path):
    """清空数据库并重置自增ID"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 开始事务
        cursor.execute("BEGIN TRANSACTION")
        
        # 清空主表数据
        cursor.execute("DELETE FROM motosim_records")
        deleted_count = cursor.rowcount
        
        # 重置自增ID
        cursor.execute("DELETE FROM sqlite_sequence WHERE name='motosim_records'")
        
        # 提交事务
        cursor.execute("COMMIT")
        conn.close()
        
        return deleted_count
    except Exception as e:
        return None, str(e)

def main():
    """主函数"""
    print("🗄️  摩托车模拟登记表管理系统 - 数据库清空工具")
    print("=" * 60)
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 数据库路径
    db_path = "database/motosim.db"
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请确保数据库文件路径正确")
        return
    
    # 获取当前数据库统计信息
    print("📊 当前数据库状态:")
    stats = get_database_stats(db_path)
    
    if stats is None:
        print("❌ 无法读取数据库信息，可能数据库文件损坏")
        return
    
    print(f"   总记录数: {stats['total_records']:,} 条")
    print(f"   当前自增ID: {stats['current_id']}")
    if stats['total_records'] > 0:
        print(f"   最早记录: {stats['earliest_time']}")
        print(f"   最新记录: {stats['latest_time']}")
    print()
    
    if stats['total_records'] == 0:
        print("✅ 数据库已经是空的，无需清空")
        return
    
    # 警告信息
    print("⚠️  警告信息:")
    print("   此操作将永久删除所有数据记录")
    print("   此操作将重置自增ID从1开始")
    print("   此操作不可逆转！")
    print()
    
    # 询问是否创建备份
    backup_choice = input("🔄 是否创建数据库备份？(y/N): ").lower().strip()
    backup_path = None
    
    if backup_choice in ['y', 'yes', '是', 'Y']:
        print("📦 正在创建备份...")
        backup_path = backup_database(db_path)
        if backup_path:
            print(f"✅ 备份已创建: {backup_path}")
        else:
            print("❌ 备份创建失败")
            continue_choice = input("⚠️  是否继续执行清空操作？(y/N): ").lower().strip()
            if continue_choice not in ['y', 'yes', '是', 'Y']:
                print("🚫 操作已取消")
                return
        print()
    
    # 三重确认机制
    print("🔐 安全确认（需要连续3次确认）:")
    
    # 第一次确认
    confirm1 = input(f"第1次确认 - 确定要删除 {stats['total_records']:,} 条记录吗？(输入 'DELETE' 确认): ").strip()
    if confirm1 != 'DELETE':
        print("🚫 操作已取消 - 第1次确认失败")
        return
    
    # 第二次确认
    confirm2 = input("第2次确认 - 确定要重置自增ID吗？(输入 'RESET' 确认): ").strip()
    if confirm2 != 'RESET':
        print("🚫 操作已取消 - 第2次确认失败")
        return
    
    # 第三次确认
    confirm3 = input("第3次确认 - 最后确认，此操作不可逆！(输入 'CONFIRM' 确认): ").strip()
    if confirm3 != 'CONFIRM':
        print("🚫 操作已取消 - 第3次确认失败")
        return
    
    print()
    print("🚀 开始执行清空操作...")
    
    # 执行清空操作
    result = clear_database(db_path)
    
    if isinstance(result, tuple):
        # 操作失败
        error_msg = result[1]
        print(f"❌ 清空失败: {error_msg}")
        return
    
    # 操作成功
    deleted_count = result
    print(f"✅ 清空成功！")
    print(f"   删除记录数: {deleted_count:,} 条")
    print(f"   自增ID已重置: 从 {stats['current_id']} 重置为 1")
    
    # 验证清空结果
    print()
    print("🔍 验证清空结果:")
    new_stats = get_database_stats(db_path)
    
    if new_stats:
        print(f"   当前记录数: {new_stats['total_records']} 条")
        print(f"   当前自增ID: {new_stats['current_id']}")
        
        if new_stats['total_records'] == 0 and new_stats['current_id'] == 0:
            print("✅ 数据库清空验证成功")
        else:
            print("⚠️  清空结果异常，请检查数据库状态")
    else:
        print("❌ 无法验证清空结果")
    
    # 显示备份信息
    if backup_path:
        print(f"\n💾 备份文件保存位置: {backup_path}")
        print("   如需恢复数据，请使用备份文件")
    
    print()
    print("🎉 数据库清空操作完成！")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🚫 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {e}")
        sys.exit(1) 