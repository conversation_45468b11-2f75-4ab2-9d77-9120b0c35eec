#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作表选择功能验证脚本
验证新增的工作表选择流程是否正常工作
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import os
import time
from datetime import datetime

def test_worksheet_selection_workflow():
    """测试完整的工作表选择工作流程"""
    print("🔍 验证工作表选择功能...")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    
    # 测试1: 检查主页是否包含文件上传表单
    print("📱 测试1: 检查主页文件上传功能...")
    try:
        response = urllib.request.urlopen(f"{base_url}/")
        content = response.read().decode('utf-8')
        
        if 'input type="file"' in content and 'name="file"' in content:
            print("✅ 主页文件上传表单正常")
        else:
            print("❌ 主页文件上传表单异常")
            
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
    
    # 测试2: 检查新路由是否存在
    print("\n🔗 测试2: 检查新增的路由端点...")
    
    # 检查 /process_sheets 路由 (应该返回405因为GET方法不允许)
    try:
        response = urllib.request.urlopen(f"{base_url}/process_sheets")
    except urllib.error.HTTPError as e:
        if e.code == 405:  # Method Not Allowed
            print("✅ /process_sheets 路由存在 (仅接受POST)")
        else:
            print(f"❌ /process_sheets 路由异常: {e.code}")
    except Exception as e:
        print(f"❌ /process_sheets 路由测试失败: {e}")
    
    # 测试3: 检查模板文件
    print("\n📄 测试3: 检查模板文件...")
    template_path = "templates/sheet_selection.html"
    if os.path.exists(template_path):
        print("✅ sheet_selection.html 模板文件存在")
        
        # 检查模板内容
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
            
        checks = [
            ('工作表选择', '包含工作表选择相关文本'),
            ('worksheet-checkbox', '包含工作表复选框'),
            ('selected_sheets', '包含工作表选择表单'),
            ('process_sheets', '包含处理工作表的表单提交'),
            ('selectAll()', '包含全选功能'),
            ('bootstrap', '使用Bootstrap样式')
        ]
        
        for check, description in checks:
            if check.lower() in template_content.lower():
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少: {description}")
    else:
        print("❌ sheet_selection.html 模板文件不存在")
    
    # 测试4: 检查JavaScript功能
    print("\n🎨 测试4: 检查JavaScript功能增强...")
    js_path = "static/js/main.js"
    if os.path.exists(js_path):
        with open(js_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
            
        js_checks = [
            ('WorksheetSelection', 'WorksheetSelection对象'),
            ('selectAll', '全选功能'),
            ('selectNone', '取消全选功能'),
            ('selectReverse', '反选功能'),
            ('updateSelectionState', '选择状态更新'),
            ('selectByTimePattern', '按时段选择'),
            ('selectByDatePattern', '按日期选择')
        ]
        
        for check, description in js_checks:
            if check in js_content:
                print(f"✅ 包含{description}")
            else:
                print(f"❌ 缺少{description}")
    else:
        print("❌ main.js 文件不存在")
    
    # 测试5: 检查应用程序配置
    print("\n⚙️  测试5: 检查应用程序配置...")
    app_py_path = "app.py"
    if os.path.exists(app_py_path):
        with open(app_py_path, 'r', encoding='utf-8') as f:
            app_content = f.read()
            
        app_checks = [
            ('get_worksheet_info', '获取工作表信息函数'),
            ('clean_and_extract_selected_data', '处理选中工作表函数'),
            ('@app.route(\'/process_sheets\'', '处理工作表路由'),
            ('selected_sheets', '选中工作表处理'),
            ('render_template(\'sheet_selection.html\'', '工作表选择模板渲染')
        ]
        
        for check, description in app_checks:
            if check in app_content:
                print(f"✅ 包含{description}")
            else:
                print(f"❌ 缺少{description}")
    else:
        print("❌ app.py 文件不存在")
    
    # 测试6: 创建工作流程说明
    print("\n📋 测试6: 新工作流程说明...")
    print("新的Excel文件导入流程:")
    print("1. 用户在主页上传Excel文件")
    print("2. 系统解析文件，显示工作表选择页面")
    print("3. 用户选择需要导入的工作表")
    print("4. 系统处理选中的工作表并导入数据")
    print("5. 显示导入结果页面")
    
    # 测试7: 检查上传目录
    print("\n📁 测试7: 检查上传目录...")
    upload_dir = "uploads"
    if os.path.exists(upload_dir):
        print(f"✅ 上传目录存在: {upload_dir}")
        file_count = len([f for f in os.listdir(upload_dir) if os.path.isfile(os.path.join(upload_dir, f))])
        print(f"📊 当前临时文件数量: {file_count}")
        
        if file_count > 10:
            print("⚠️  建议清理过多的临时文件")
    else:
        print(f"⚠️  上传目录不存在，将在首次上传时自动创建")
    
    print("\n" + "=" * 60)
    print("🎉 工作表选择功能验证完成！")
    print("\n💡 使用说明:")
    print("1. 访问 http://localhost:5001/ ")
    print("2. 上传Excel文件测试新的工作表选择功能")
    print("3. 体验多工作表选择和智能推荐功能")
    
def main():
    """主函数"""
    print(f"🔍 工作表选择功能验证 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查Flask应用是否运行
    try:
        response = urllib.request.urlopen("http://localhost:5001/")
        print("✅ Flask应用正在运行")
    except Exception as e:
        print("❌ Flask应用未运行，请先启动应用: python3 app.py")
        return
    
    test_worksheet_selection_workflow()

if __name__ == "__main__":
    main() 