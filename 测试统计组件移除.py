#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试右侧统计组件移除效果
验证"Excel数据导入"页面右侧统计组件是否已成功移除
"""

import os
from datetime import datetime

def check_index_template():
    """检查index.html模板的修改"""
    template_path = "templates/index.html"
    
    if not os.path.exists(template_path):
        print("❌ 模板文件不存在")
        return False
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 检查模板修改状态:")
        
        # 检查是否移除了右侧统计组件
        removed_components = [
            ('col-lg-4', '右侧4列布局'),
            ('数据库状态', '数据库状态卡片'),
            ('总记录数', '总记录数统计'),
            ('按时段统计', '时段统计组件'),
            ('按日期统计', '日期统计组件'),
            ('最新记录', '最新记录卡片'),
            ('refreshStats', 'refreshStats函数调用'),
        ]
        
        for component, description in removed_components:
            if component in content:
                print(f"⚠️  仍然包含{description}")
            else:
                print(f"✅ 已移除{description}")
        
        # 检查是否正确调整了布局
        layout_checks = [
            ('col-12', '全宽度布局'),
            ('文件上传区域', '更新后的注释'),
        ]
        
        print("\n📐 检查布局调整:")
        for check, description in layout_checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少{description}")
        
        return True
    except Exception as e:
        print(f"❌ 无法读取模板文件: {e}")
        return False

def check_app_route():
    """检查app.py中index路由的修改"""
    app_path = "app.py"
    
    if not os.path.exists(app_path):
        print("❌ app.py文件不存在")
        return False
    
    try:
        with open(app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n🔍 检查app.py修改状态:")
        
        # 检查index路由是否已简化
        route_checks = [
            ('return render_template(\'index.html\')', '简化的模板渲染'),
        ]
        
        for check, description in route_checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少{description}")
        
        # 检查是否移除了不必要的数据获取
        removed_code = [
            ('db_stats = get_database_stats()', '数据库统计获取'),
            ('db_stats=db_stats', '统计数据传递'),
        ]
        
        print("\n清理检查:")
        for check, description in removed_code:
            if check in content:
                print(f"⚠️  仍然包含{description}")
            else:
                print(f"✅ 已移除{description}")
        
        return True
    except Exception as e:
        print(f"❌ 无法读取app.py文件: {e}")
        return False

def analyze_changes():
    """分析修改效果"""
    print("\n📊 修改效果分析:")
    
    print("移除的组件:")
    print("- 右侧4列布局容器 (col-lg-4)")
    print("- 数据库状态卡片")
    print("- 总记录数显示")
    print("- 按时段统计图表")
    print("- 按日期统计图表")
    print("- 最新记录列表")
    print("- 刷新统计按钮")
    print("- refreshStats() JavaScript函数")
    
    print("\n布局调整:")
    print("- 左侧布局从 col-lg-8 改为 col-12")
    print("- 文件上传区域现在占据全宽度")
    print("- 移除了左右分栏布局")
    
    print("\n性能优化:")
    print("- 移除了index路由中的数据库统计查询")
    print("- 减少了页面加载时的数据库访问")
    print("- 简化了模板渲染过程")

def test_page_structure():
    """测试页面结构"""
    print("\n🏗️  预期页面结构:")
    
    structure = """
    页面标题: Excel数据导入
    ├── 文件上传区域 (全宽度)
    │   ├── 上传Excel文件卡片
    │   │   ├── 文件选择输入框
    │   │   ├── 开始导入按钮
    │   │   └── 文件信息提示
    │   └── 使用说明
    └── (无右侧统计组件)
    """
    
    print(structure)

def main():
    """主函数"""
    print("🗑️  统计组件移除验证工具")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查模板修改
    template_ok = check_index_template()
    
    # 检查路由修改
    route_ok = check_app_route()
    
    # 分析修改效果
    analyze_changes()
    
    # 测试页面结构
    test_page_structure()
    
    print("\n💡 验证建议:")
    print("1. 重启Flask应用以应用修改:")
    print("   停止当前应用，重新运行 python3 app.py")
    print()
    print("2. 访问主页测试效果:")
    print("   访问 http://localhost:5001/")
    print("   验证右侧统计组件已被移除")
    print()
    print("3. 检查页面布局:")
    print("   - 文件上传区域应占据全宽度")
    print("   - 页面应该更简洁，专注于文件上传功能")
    print("   - 没有数据库统计信息显示")
    
    if template_ok and route_ok:
        print("\n✅ 统计组件移除验证通过！")
    else:
        print("\n❌ 检查发现问题，请查看上述详情")
    
    print("\n" + "=" * 60)
    print("🎉 验证工具运行完成！")

if __name__ == "__main__":
    main() 