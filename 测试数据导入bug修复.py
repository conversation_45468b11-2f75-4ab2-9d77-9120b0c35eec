#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据导入BUG修复效果
验证修复后的数据导入是否使用追加模式而非替换模式
"""

import sqlite3
import os
from datetime import datetime

def get_current_record_count():
    """获取当前数据库记录数"""
    db_path = "database/motosim.db"
    
    if not os.path.exists(db_path):
        return 0, "数据库文件不存在"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='motosim_records'")
        if not cursor.fetchone():
            conn.close()
            return 0, "表不存在"
        
        # 获取记录数
        cursor.execute("SELECT COUNT(*) FROM motosim_records")
        count = cursor.fetchone()[0]
        
        # 获取自增ID
        cursor.execute("SELECT seq FROM sqlite_sequence WHERE name='motosim_records'")
        current_id = cursor.fetchone()
        current_id = current_id[0] if current_id else 0
        
        conn.close()
        return count, current_id
    except Exception as e:
        return None, str(e)

def check_app_code():
    """检查app.py代码是否已经修复"""
    try:
        with open("app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复标志
        checks = [
            ('CREATE TABLE IF NOT EXISTS motosim_records', '使用CREATE TABLE IF NOT EXISTS'),
            ('追加模式', '函数注释标明追加模式'),
            ('before_count = cursor.fetchone()[0]', '获取导入前记录数'),
            ('inserted_count += 1', '统计插入数量'),
            ('数据库现有', '修改后的成功消息'),
        ]
        
        print("🔍 检查代码修复状态:")
        for check, description in checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少{description}")
        
        # 检查危险代码是否已移除
        dangerous_checks = [
            ('DROP TABLE IF EXISTS motosim_records', '危险的DROP TABLE语句'),
            ('删除旧表并创建新表', '旧的注释说明'),
        ]
        
        print("\n🚨 检查危险代码移除状态:")
        for check, description in dangerous_checks:
            if check in content:
                print(f"❌ 仍然包含{description}")
            else:
                print(f"✅ 已移除{description}")
                
        return True
    except Exception as e:
        print(f"❌ 无法读取app.py文件: {e}")
        return False

def analyze_bug_impact():
    """分析BUG影响和修复效果"""
    print("\n📊 BUG影响分析:")
    
    # 获取当前状态
    count, id_or_error = get_current_record_count()
    
    if count is None:
        print(f"❌ 无法获取数据库状态: {id_or_error}")
        return
    
    print(f"📈 当前数据库状态:")
    print(f"   记录数: {count} 条")
    if isinstance(id_or_error, int):
        print(f"   自增ID: {id_or_error}")
    
    print(f"\n🔄 修复前后对比:")
    print(f"修复前行为:")
    print(f"  导入新数据 → 清空所有历史数据 → 只保留新数据 ❌")
    print(f"  数据库记录: N条 → M条 (丢失原有N条)")
    print(f"  自增ID: 重置为1")
    
    print(f"\n修复后行为:")
    print(f"  导入新数据 → 保留所有历史数据 → 追加新数据 ✅")
    print(f"  数据库记录: N条 → N+M条 (保留原有N条)")
    print(f"  自增ID: 继续递增")

def test_scenarios():
    """描述测试场景"""
    print("\n🧪 建议测试场景:")
    
    scenarios = [
        {
            'name': '场景1: 首次导入测试',
            'steps': [
                '1. 清空数据库 (使用清空脚本)',
                '2. 导入第一个Excel文件',
                '3. 验证记录数 = 导入数据条数',
                '4. 验证自增ID从1开始'
            ]
        },
        {
            'name': '场景2: 追加导入测试',
            'steps': [
                '1. 记录当前数据库状态 (N条记录)',
                '2. 导入新的Excel文件 (M条新数据)',
                '3. 验证记录数 = N + M',
                '4. 验证新记录自增ID继续递增',
                '5. 验证历史数据完整保留'
            ]
        },
        {
            'name': '场景3: 重复导入测试',
            'steps': [
                '1. 导入相同的Excel文件两次',
                '2. 验证会产生重复记录',
                '3. 确认这是预期行为（追加模式）'
            ]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        for step in scenario['steps']:
            print(f"   {step}")

def main():
    """主函数"""
    print("🔧 数据导入BUG修复验证工具")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查代码修复状态
    check_app_code()
    
    # 分析BUG影响
    analyze_bug_impact()
    
    # 测试场景建议
    test_scenarios()
    
    print("\n💡 验证建议:")
    print("1. 首先备份当前数据库:")
    print("   cp database/motosim.db database/motosim_backup_before_test.db")
    print()
    print("2. 重启Flask应用以应用代码修复:")
    print("   停止当前应用，重新运行 python3 app.py")
    print()
    print("3. 通过Web界面测试导入功能:")
    print("   访问 http://localhost:5001/")
    print("   上传Excel文件并观察结果消息")
    print()
    print("4. 验证修复效果:")
    print("   - 导入成功消息应显示: '成功导入 X 条新记录，数据库现有 Y 条记录（导入前: Z 条）'")
    print("   - 数据库记录数应该增加而不是被替换")
    print("   - 历史数据应该完整保留")
    
    print("\n" + "=" * 60)
    print("🎉 BUG修复验证工具运行完成！")
    print("请按照上述建议进行实际测试验证。")

if __name__ == "__main__":
    main() 