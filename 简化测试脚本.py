#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摩托车模拟登记表管理系统 - 简化测试脚本
使用Python标准库，无需安装额外依赖
"""

import urllib.request
import urllib.error
import json
import os
import time
from datetime import datetime


def test_web_application():
    """测试Web应用的主要功能"""
    base_url = "http://localhost:5001"
    
    print("🚀 开始测试摩托车模拟登记表管理系统")
    print("=" * 60)
    
    # 测试1: 检查主页
    print("📱 测试1: 检查主页访问...")
    try:
        response = urllib.request.urlopen(f"{base_url}/", timeout=5)
        if response.status == 200:
            content = response.read().decode('utf-8')
            print("✅ 主页访问成功")
            if "摩托车模拟登记系统" in content:
                print("✅ 页面内容正确加载")
            else:
                print("⚠️  页面内容可能有问题")
        else:
            print(f"❌ 主页访问失败，状态码: {response.status}")
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
    
    print()
    
    # 测试2: 检查API接口
    print("🔗 测试2: 检查API接口...")
    try:
        response = urllib.request.urlopen(f"{base_url}/api/stats", timeout=5)
        if response.status == 200:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            print("✅ API接口访问成功")
            print(f"📊 总记录数: {data.get('total_count', 0)}")
            
            if 'time_stats' in data:
                print("📊 时段统计:")
                for time_period, count in data['time_stats']:
                    print(f"   {time_period}: {count} 条记录")
            
            if 'date_stats' in data:
                print("📊 日期统计:")
                for date, count in data['date_stats']:
                    print(f"   {date}: {count} 条记录")
                    
        else:
            print(f"❌ API接口访问失败，状态码: {response.status}")
    except Exception as e:
        print(f"❌ API接口访问失败: {e}")
    
    print()
    
    # 测试3: 检查记录页面
    print("📋 测试3: 检查记录管理页面...")
    try:
        response = urllib.request.urlopen(f"{base_url}/records", timeout=5)
        if response.status == 200:
            content = response.read().decode('utf-8')
            print("✅ 记录页面访问成功")
            if "数据记录" in content:
                print("✅ 记录页面内容正确")
            else:
                print("⚠️  记录页面内容可能有问题")
        else:
            print(f"❌ 记录页面访问失败，状态码: {response.status}")
    except Exception as e:
        print(f"❌ 记录页面访问失败: {e}")
    
    print()
    
    # 测试4: 检查静态资源
    print("🎨 测试4: 检查静态资源...")
    static_files = [
        "/static/css/style.css",
        "/static/js/main.js"
    ]
    
    for static_file in static_files:
        try:
            request = urllib.request.Request(f"{base_url}{static_file}")
            request.get_method = lambda: 'HEAD'
            response = urllib.request.urlopen(request, timeout=5)
            if response.status == 200:
                print(f"✅ {static_file} 加载成功")
            else:
                print(f"⚠️  {static_file} 访问状态: {response.status}")
        except Exception as e:
            print(f"❌ {static_file} 访问失败: {e}")


def check_project_files():
    """检查项目文件结构"""
    print("\n📁 检查项目文件结构...")
    print("-" * 40)
    
    required_files = [
        "app.py",
        "excel_to_sqlite.py",
        "templates/base.html",
        "templates/index.html",
        "templates/result.html",
        "templates/records.html",
        "static/css/style.css",
        "static/js/main.js",
        "database/motosim.db"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  发现 {len(missing_files)} 个缺失文件")
    else:
        print("\n✅ 所有必需文件都存在")


def check_flask_status():
    """检查Flask应用运行状态"""
    print("\n🌐 检查Flask应用状态...")
    print("-" * 40)
    
    try:
        response = urllib.request.urlopen("http://localhost:5001/", timeout=3)
        if response.status == 200:
            print("✅ Flask应用正在运行 (端口5001)")
            return True
        else:
            print(f"⚠️  Flask应用响应异常，状态码: {response.status}")
            return False
    except urllib.error.URLError as e:
        if "Connection refused" in str(e):
            print("❌ Flask应用未运行")
            print("   请执行: python3 app.py")
        else:
            print(f"❌ 连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False


def display_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("🎯 Web应用使用指南")
    print("=" * 60)
    
    print("\n📍 访问地址:")
    print("   主页: http://localhost:5001/")
    print("   记录管理: http://localhost:5001/records")
    print("   API统计: http://localhost:5001/api/stats")
    
    print("\n🔧 启动命令:")
    print("   cd /Users/<USER>/Project/MotoSim")
    print("   python3 app.py")
    
    print("\n📤 使用步骤:")
    print("   1. 访问主页 http://localhost:5001/")
    print("   2. 拖拽或选择Excel文件(.xlsx或.xls)")
    print("   3. 点击'开始导入'按钮")
    print("   4. 查看导入结果和数据预览")
    print("   5. 使用记录管理页面搜索和筛选数据")
    
    print("\n🎨 界面特色:")
    print("   ✨ 现代化Bootstrap 5设计")
    print("   📱 完全响应式布局")
    print("   🔍 实时搜索和筛选")
    print("   📊 多维度数据统计")
    print("   📁 CSV格式数据导出")


def main():
    """主函数"""
    print("🏍️  摩托车模拟登记表管理系统 - 简化测试工具")
    print("📦 使用Python标准库，无需额外依赖")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n" + "=" * 60)
    
    # 检查项目文件
    check_project_files()
    
    print("\n" + "=" * 60)
    
    # 检查Flask状态
    flask_running = check_flask_status()
    
    if flask_running:
        print("\n" + "=" * 60)
        # 测试Web应用
        print("🌐 开始测试Web应用功能...")
        time.sleep(1)
        test_web_application()
    else:
        print("\n⚠️  跳过功能测试，请先启动Flask应用")
    
    # 显示使用说明
    display_usage_instructions()
    
    print("\n" + "=" * 60)
    if flask_running:
        print("🎉 测试完成！所有功能正常工作")
    else:
        print("⚠️  测试未完成，请启动Flask应用后重新测试")
    
    print("\n如果遇到问题，请检查:")
    print("  1. Flask应用是否正在运行 (python3 app.py)")
    print("  2. 端口5001是否被其他程序占用")
    print("  3. 所有必需的文件是否存在")
    print("\n🚀 开始使用您的Web应用吧！")


if __name__ == "__main__":
    main() 