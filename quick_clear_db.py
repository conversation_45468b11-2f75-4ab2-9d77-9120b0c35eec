#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摩托车模拟登记表管理系统 - 快速清空数据库脚本
功能：快速清空数据库所有记录并重置自增ID（简化版）
"""

import sqlite3
import os
from datetime import datetime

def get_current_stats():
    """获取当前数据库状态"""
    db_path = "database/motosim.db"
    
    if not os.path.exists(db_path):
        return None, "数据库文件不存在"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM motosim_records")
        count = cursor.fetchone()[0]
        
        cursor.execute("SELECT seq FROM sqlite_sequence WHERE name='motosim_records'")
        current_id = cursor.fetchone()
        current_id = current_id[0] if current_id else 0
        
        conn.close()
        return count, current_id
    except Exception as e:
        return None, str(e)

def quick_clear_database():
    """快速清空数据库"""
    db_path = "database/motosim.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 开始事务
        cursor.execute("BEGIN TRANSACTION")
        
        # 清空数据
        cursor.execute("DELETE FROM motosim_records")
        deleted_count = cursor.rowcount
        
        # 重置自增ID
        cursor.execute("DELETE FROM sqlite_sequence WHERE name='motosim_records'")
        
        # 提交事务
        cursor.execute("COMMIT")
        conn.close()
        
        return deleted_count, None
    except Exception as e:
        return None, str(e)

def main():
    """主函数"""
    print("🚀 快速清空数据库工具")
    print("=" * 50)
    print(f"⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 获取当前状态
    result = get_current_stats()
    if result[0] is None:
        print(f"❌ 错误: {result[1]}")
        return
    
    record_count, current_id = result
    print(f"📊 当前状态:")
    print(f"   记录数: {record_count:,} 条")
    print(f"   自增ID: {current_id}")
    print()
    
    if record_count == 0:
        print("✅ 数据库已经是空的")
        return
    
    # 简化确认
    print("⚠️  警告: 此操作将删除所有数据并重置ID！")
    confirm = input(f"确定要删除 {record_count:,} 条记录吗？(输入 'YES' 确认): ").strip()
    
    if confirm != 'YES':
        print("🚫 操作已取消")
        return
    
    print("🚀 执行清空操作...")
    
    # 执行清空
    result = quick_clear_database()
    if result[1] is not None:
        print(f"❌ 清空失败: {result[1]}")
        return
    
    deleted_count = result[0]
    print(f"✅ 清空成功！")
    print(f"   删除记录: {deleted_count:,} 条")
    print(f"   ID已重置: {current_id} → 1")
    
    # 验证结果
    result = get_current_stats()
    if result[0] is not None:
        new_count, new_id = result
        print(f"🔍 验证结果: {new_count} 条记录, ID: {new_id}")

if __name__ == "__main__":
    main() 