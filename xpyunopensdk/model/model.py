import sys
import os
import logging
import requests
import json

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def setup_util_path():
    """设置util路径并返回正确的导入路径"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # 打包后的环境
            sdk_path = os.path.join(sys._MEIPASS, 'xpyunopensdk')
            logger.debug(f"打包环境SDK路径: {sdk_path}")
            
            # 确保SDK目录存在
            if not os.path.exists(sdk_path):
                raise ImportError(f"SDK目录不存在: {sdk_path}")
            
            # 添加SDK根目录到sys.path
            if sdk_path not in sys.path:
                sys.path.insert(0, sdk_path)
                logger.debug(f"已添加SDK路径到sys.path: {sdk_path}")
            
            return 'xpyunopensdk.util.xputil'
        else:
            # 开发环境
            current_dir = os.path.dirname(os.path.dirname(__file__))
            logger.debug(f"开发环境路径: {current_dir}")
            
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
                logger.debug(f"已添加开发环境路径到sys.path: {current_dir}")
            
            return 'xpyunopensdk.util.xputil'
    except Exception as e:
        logger.error(f"设置util路径时出错: {str(e)}")
        raise

# 导入util模块
try:
    import_path = setup_util_path()
    logger.debug(f"使用导入路径: {import_path}")
    
    # 动态导入模块
    import importlib
    util = importlib.import_module(import_path)
    logger.debug("成功导入util模块")
except Exception as e:
    logger.error(f"导入util模块时出错: {str(e)}")
    raise

# API接口地址
API_URL = "https://open.xpyun.net/api/openapi/xprinter/print"

class RestRequest:
    user = ""
    userKey = ""
    timestamp = 0
    sign = ""
    debug = 0

    def __init__(self, user, userKey):
        self.user = user
        self.userKey = userKey
        self.timestamp = util.getMillisecond()

    def generateSign(self):
        self.sign = util.sign(self.user + self.userKey + str(self.timestamp))


class AddPrinterRequestItem:
    # 打印机编号
    sn = ""
    # 打印机名称
    name = ""

    def getSn(self):
        return self.sn

    def getName(self):
        return self.name


class AddPrinterRequest(RestRequest):
    # 打印机信息列表
    items = []


class DelPrinterRequest(RestRequest):
    # 打印机编号集合
    snlist = []


class PrintRequest(RestRequest):
    # 打印机编号
    sn = ""

    # 打印内容,不能超过5000字节
    content = ""

    # 打印份数，默认为1
    copies = 1

    # 打印模式，默认为0
    mode = 1

    # 声音播放模式，0 为取消订单模式，1 为静音模式，2 为来单播放模式，默认为 1 静音模式
    voice = 1

    # 支付方式41~55：支付宝 微信 ...
    payType = 0

    # 支付与否59~61：退款 到账 消费
    payMode = 0

    # 支付金额
    money = 0.0

    def send(self):
        """发送打印请求"""
        try:
            # 准备请求数据
            data = {
                "user": self.user,
                "userKey": self.userKey,
                "timestamp": self.timestamp,
                "sign": self.sign,
                "debug": self.debug,
                "sn": self.sn,
                "content": self.content,
                "copies": self.copies,
                "mode": self.mode,
                "voice": self.voice,
                "payType": self.payType,
                "payMode": self.payMode,
                "money": self.money
            }

            logger.debug(f"发送打印请求数据: {json.dumps(data, ensure_ascii=False)}")

            # 发送HTTP请求
            response = requests.post(API_URL, json=data)
            response_json = response.json()

            logger.debug(f"收到响应数据: {json.dumps(response_json, ensure_ascii=False)}")

            # 创建响应对象
            resp_content = XPYunRespContent()
            resp_content.code = response_json.get('code', -1)
            resp_content.msg = response_json.get('msg', '未知错误')
            resp_content.data = response_json.get('data', '')
            resp_content.serverExecutedTime = response_json.get('serverExecutedTime', 0)

            if resp_content.code == 0:
                logger.info("打印请求发送成功")
            else:
                logger.error(f"打印请求失败: {resp_content.msg}")

            return resp_content

        except Exception as e:
            logger.error(f"发送打印请求时出错: {str(e)}")
            # 创建错误响应
            resp_content = XPYunRespContent()
            resp_content.code = -1
            resp_content.msg = f"请求失败: {str(e)}"
            return resp_content


class SetVoiceTypeRequest(RestRequest):
    # 打印机编号
    sn = ""

    # 声音类型： 0真人语音（大） 1真人语音（中） 2真人语音（小） 3 嘀嘀声  4 静音
    voiceType = 0


class UpdPrinterRequest(RestRequest):
    # 打印机编号
    sn = ""

    # 打印机名称
    name = ""


class PrinterRequest(RestRequest):
    # 打印机编号
    sn = ""


class QueryOrderStateRequest(RestRequest):
    # 订单编号
    orderId = ""


class QueryOrderStatisRequest(RestRequest):
    # 打印机编号
    sn = ""

    # 查询日期，格式YY-MM-DD，如：2016-09-20
    date = ""


class VoiceRequest(RestRequest):
    # 打印机编号
    sn = ""

    # 支付方式41~55：支付宝 微信 ...
    payType = 0

    # 支付与否59~61：退款 到账 消费
    payMode = 0

    # 支付金额
    money = 0.0


class XPYunResp:
    httpStatusCode = 0
    content = ""


class XPYunRespContent:
    code = 0
    msg = ""
    data = ""
    serverExecutedTime = 0
