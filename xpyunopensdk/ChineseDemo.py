# import xpyunopensdk.chinese.xpsdkdemo as demo
import chinese.printerExample as chinese

if __name__ == '__main__':
    print("开始打印测试...")
    chinese.printFontAlign()
    print("打印测试完成！")
    # demo.addPrintersTest()

    # demo.setVoiceTypeTest()

    # demo.delPrintersTest()

    # demo.updPrinterTest()

    # demo.xpYunDelPrinterQueueTest()
    #
    # demo.xpYunQueryOrderStateTest()
    #
    # demo.queryOrderStatisTest()
    #
    # demo.xpYunQueryPrinterStatusTest()
    #
    # demo.xpYunPlayVoiceTest()
    #
    # chinese.printFontAlignVoiceSupport()
    #
    # chinese.printComplexReceipt()
    #
    # chinese.printComplexReceiptVoiceSupport()
    # chinese.posPrintLabel()
    # chinese.printLabel()

    # demo.xpYunControlBoxTest()
