#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摩托车模拟登记表数据导入Web应用
使用Flask + Jinja2 + Bootstrap构建的数据导入界面
"""

import os
import sqlite3
from datetime import datetime
from flask import Flask, render_template, request, flash, redirect, url_for, jsonify
from werkzeug.utils import secure_filename
import pandas as pd
import re


# Flask应用配置
app = Flask(__name__)
app.config['SECRET_KEY'] = 'motosim_secret_key_2024'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB最大文件大小

# 允许上传的文件类型
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}


def allowed_file(filename):
    """检查文件类型是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def get_worksheet_info(excel_file):
    """获取Excel文件的工作表信息和预览数据"""
    try:
        excel_data = pd.ExcelFile(excel_file)
        worksheets_info = []
        
        for sheet_name in excel_data.sheet_names:
            # 读取工作表的前几行作为预览
            df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=5)
            
            # 从第一行提取日期和时段信息
            raw_date_info = str(df.iloc[0, 0]) if not df.empty and not pd.isna(df.iloc[0, 0]) else sheet_name
            
            # 使用正则表达式解析日期和时段
            date_pattern = r'模拟日期(\d+/\d+)(上午|下午)'
            match = re.search(date_pattern, raw_date_info)
            
            if match:
                simulation_date = match.group(1)  # 提取日期部分
                time_period = match.group(2)     # 提取时段部分
            else:
                simulation_date = sheet_name
                time_period = "未知时段"
            
            # 计算实际数据行数（跳过前两行后的有效数据）
            full_df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            # 灵活处理列数，根据实际列数决定列名
            actual_columns = len(full_df.columns)
            if actual_columns >= 6:
                # 标准6列格式或更多列
                expected_columns = ['序号', '驾校', '教练', '学员', '车型', '模拟科目']
                if actual_columns > 6:
                    # 如果有额外列，添加通用列名
                    for i in range(6, actual_columns):
                        expected_columns.append(f'附加列{i-5}')
                
                # 设置列名并获取数据部分
                temp_df = full_df.copy()
                temp_df.columns = expected_columns
                data_part = temp_df.iloc[2:].reset_index(drop=True)
                
                # 验证关键列数据完整性：学员、车型、模拟科目三列都必须有数据
                valid_rows = data_part.dropna(subset=['学员', '车型', '模拟科目'])
                # 进一步过滤空字符串
                valid_rows = valid_rows[
                    (valid_rows['学员'].astype(str).str.strip() != '') &
                    (valid_rows['车型'].astype(str).str.strip() != '') &
                    (valid_rows['模拟科目'].astype(str).str.strip() != '')
                ]
                data_rows = len(valid_rows)
            else:
                # 列数不足，使用通用列名
                expected_columns = [f'列{i+1}' for i in range(actual_columns)]
                data_rows = len(full_df.iloc[1:].dropna(subset=[full_df.columns[0]]))  # 可能只有1行标题
            
            worksheets_info.append({
                'name': sheet_name,
                'simulation_date': simulation_date,
                'time_period': time_period,
                'data_rows': data_rows,
                'raw_date_info': raw_date_info,
                'preview_data': df.head(3).to_dict('records') if not df.empty else []
            })
        
        # 按工作表信息智能排序：优先按日期，然后按时段（下午在前）
        def sort_key(worksheet):
            try:
                # 尝试解析日期（如"8/1"转换为可比较的元组）
                date_parts = worksheet['simulation_date'].split('/')
                if len(date_parts) == 2:
                    month, day = int(date_parts[0]), int(date_parts[1])
                    # 时段权重：下午=0, 上午=1 （下午排在前面）
                    time_weight = 0 if worksheet['time_period'] == '下午' else 1
                    return (-month, -day, time_weight)  # 负号实现倒序
            except:
                pass
            # 降级排序：按工作表名称倒序
            return (0, 0, worksheet['name'])
        
        worksheets_info.sort(key=sort_key)
        
        return worksheets_info, None
        
    except Exception as e:
        return None, f"读取Excel文件时出错: {str(e)}"


def clean_and_extract_selected_data(excel_file, selected_sheets):
    """从Excel文件中清理和提取用户选择的工作表数据"""
    all_data = []
    
    try:
        excel_data = pd.ExcelFile(excel_file)
        
        # 只处理用户选择的工作表
        for sheet_name in selected_sheets:
            if sheet_name not in excel_data.sheet_names:
                continue
                
            # 读取工作表
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            # 从第一行提取日期和时段信息
            raw_date_info = str(df.iloc[0, 0]) if not pd.isna(df.iloc[0, 0]) else sheet_name
            
            # 使用正则表达式解析日期和时段
            date_pattern = r'模拟日期(\d+/\d+)(上午|下午)'
            match = re.search(date_pattern, raw_date_info)
            
            if match:
                simulation_date = match.group(1)  # 提取日期部分
                time_period = match.group(2)     # 提取时段部分
            else:
                simulation_date = sheet_name
                time_period = "未知时段"
            
            # 灵活设置列名，根据实际列数决定
            actual_columns = len(df.columns)
            if actual_columns >= 6:
                # 标准6列格式或更多列
                expected_columns = ['序号', '驾校', '教练', '学员', '车型', '模拟科目']
                if actual_columns > 6:
                    # 如果有额外列，添加通用列名
                    for i in range(6, actual_columns):
                        expected_columns.append(f'附加列{i-5}')
                df.columns = expected_columns
                # 删除前两行并过滤空行
                df = df.iloc[2:].reset_index(drop=True)
                
                # 验证关键列数据完整性：学员、车型、模拟科目三列都必须有数据
                df = df.dropna(subset=['学员', '车型', '模拟科目'])
                # 进一步过滤空字符串和无效数据
                df = df[
                    (df['学员'].astype(str).str.strip() != '') &
                    (df['车型'].astype(str).str.strip() != '') &
                    (df['模拟科目'].astype(str).str.strip() != '')
                ].reset_index(drop=True)
                
                # 数据类型转换（序号列可能为空，所以不强制要求）
                df['序号'] = pd.to_numeric(df['序号'], errors='coerce')
            else:
                # 列数不足，使用通用列名
                expected_columns = [f'列{i+1}' for i in range(actual_columns)]
                df.columns = expected_columns
                # 列数不足时，可能只有1行标题
                df = df.iloc[1:].reset_index(drop=True)
                df = df.dropna(subset=[expected_columns[0]]).reset_index(drop=True)
                
                # 对第一列进行数据类型转换
                df[expected_columns[0]] = pd.to_numeric(df[expected_columns[0]], errors='coerce')
                df = df.dropna(subset=[expected_columns[0]]).reset_index(drop=True)
            
            # 添加源信息列
            df['工作表'] = sheet_name
            df['模拟日期'] = simulation_date
            df['时段'] = time_period
            
            all_data.append(df)
        
        # 合并所有数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            return combined_df, None
        else:
            return None, "没有找到有效数据"
            
    except Exception as e:
        return None, f"读取Excel文件时出错: {str(e)}"


def clean_and_extract_data(excel_file):
    """从Excel文件中清理和提取数据（复用之前的逻辑）"""
    all_data = []
    
    try:
        excel_data = pd.ExcelFile(excel_file)
        
        for sheet_name in excel_data.sheet_names:
            # 读取工作表
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            # 从第一行提取日期和时段信息
            raw_date_info = str(df.iloc[0, 0]) if not pd.isna(df.iloc[0, 0]) else sheet_name
            
            # 使用正则表达式解析日期和时段
            date_pattern = r'模拟日期(\d+/\d+)(上午|下午)'
            match = re.search(date_pattern, raw_date_info)
            
            if match:
                simulation_date = match.group(1)  # 提取日期部分
                time_period = match.group(2)     # 提取时段部分
            else:
                simulation_date = sheet_name
                time_period = "未知时段"
            
            # 灵活设置列名，根据实际列数决定
            actual_columns = len(df.columns)
            if actual_columns >= 6:
                # 标准6列格式或更多列
                expected_columns = ['序号', '驾校', '教练', '学员', '车型', '模拟科目']
                if actual_columns > 6:
                    # 如果有额外列，添加通用列名
                    for i in range(6, actual_columns):
                        expected_columns.append(f'附加列{i-5}')
                df.columns = expected_columns
                # 删除前两行并过滤空行
                df = df.iloc[2:].reset_index(drop=True)
                
                # 验证关键列数据完整性：学员、车型、模拟科目三列都必须有数据
                df = df.dropna(subset=['学员', '车型', '模拟科目'])
                # 进一步过滤空字符串和无效数据
                df = df[
                    (df['学员'].astype(str).str.strip() != '') &
                    (df['车型'].astype(str).str.strip() != '') &
                    (df['模拟科目'].astype(str).str.strip() != '')
                ].reset_index(drop=True)
                
                # 数据类型转换（序号列可能为空，所以不强制要求）
                df['序号'] = pd.to_numeric(df['序号'], errors='coerce')
            else:
                # 列数不足，使用通用列名
                expected_columns = [f'列{i+1}' for i in range(actual_columns)]
                df.columns = expected_columns
                # 列数不足时，可能只有1行标题
                df = df.iloc[1:].reset_index(drop=True)
                df = df.dropna(subset=[expected_columns[0]]).reset_index(drop=True)
                
                # 对第一列进行数据类型转换
                df[expected_columns[0]] = pd.to_numeric(df[expected_columns[0]], errors='coerce')
                df = df.dropna(subset=[expected_columns[0]]).reset_index(drop=True)
            
            # 添加源信息列
            df['工作表'] = sheet_name
            df['模拟日期'] = simulation_date
            df['时段'] = time_period
            
            all_data.append(df)
        
        # 合并所有数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            return combined_df, None
        else:
            return None, "没有找到有效数据"
            
    except Exception as e:
        return None, f"读取Excel文件时出错: {str(e)}"


def save_to_database(df, db_path="database/motosim.db"):
    """将数据保存到SQLite数据库（追加模式）"""
    try:
        # 确保database目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在，不存在则创建（追加模式）
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS motosim_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            序号 INTEGER,
            驾校 TEXT,
            教练 TEXT,
            学员 TEXT,
            车型 TEXT,
            模拟科目 TEXT,
            工作表 TEXT,
            模拟日期 TEXT,
            时段 TEXT,
            创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(create_table_sql)
        
        # 获取导入前的记录数
        cursor.execute("SELECT COUNT(*) FROM motosim_records")
        before_count = cursor.fetchone()[0]
        
        # 插入新数据（追加模式）
        inserted_count = 0
        for index, row in df.iterrows():
            insert_sql = """
            INSERT INTO motosim_records 
            (序号, 驾校, 教练, 学员, 车型, 模拟科目, 工作表, 模拟日期, 时段)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            cursor.execute(insert_sql, (
                row['序号'], row['驾校'], row['教练'], row['学员'],
                row['车型'], row['模拟科目'], row['工作表'], 
                row['模拟日期'], row['时段']
            ))
            inserted_count += 1
        
        conn.commit()
        
        # 获取最终统计信息
        cursor.execute("SELECT COUNT(*) FROM motosim_records")
        total_count = cursor.fetchone()[0]
        
        # 按时段统计
        cursor.execute("SELECT 时段, COUNT(*) FROM motosim_records GROUP BY 时段")
        time_stats = cursor.fetchall()
        
        # 按驾校统计
        cursor.execute("SELECT 驾校, COUNT(*) FROM motosim_records GROUP BY 驾校")
        school_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            'success': True,
            'total_count': total_count,
            'inserted_count': inserted_count,
            'before_count': before_count,
            'time_stats': time_stats,
            'school_stats': school_stats,
            'message': f'成功导入 {inserted_count} 条新记录，数据库现有 {total_count} 条记录（导入前: {before_count} 条）'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f'数据库操作失败: {str(e)}'
        }


def get_database_stats():
    """获取数据库统计信息"""
    try:
        conn = sqlite3.connect("database/motosim.db")
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='motosim_records'")
        if not cursor.fetchone():
            return None
        
        # 总记录数
        cursor.execute("SELECT COUNT(*) FROM motosim_records")
        total_count = cursor.fetchone()[0]
        
        # 最新记录
        cursor.execute("SELECT * FROM motosim_records ORDER BY 创建时间 DESC LIMIT 5")
        recent_records = cursor.fetchall()
        
        # 按时段统计
        cursor.execute("SELECT 时段, COUNT(*) FROM motosim_records GROUP BY 时段")
        time_stats = cursor.fetchall()
        
        # 按日期统计
        cursor.execute("SELECT 模拟日期, COUNT(*) FROM motosim_records GROUP BY 模拟日期")
        date_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            'total_count': total_count,
            'recent_records': recent_records,
            'time_stats': time_stats,
            'date_stats': date_stats
        }
        
    except Exception as e:
        return None


@app.route('/')
def index():
    """主页 - 数据导入界面"""
    return render_template('index.html')


@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传和数据导入"""
    if 'file' not in request.files:
        flash('没有选择文件', 'error')
        return redirect(url_for('index'))
    
    file = request.files['file']
    
    if file.filename == '':
        flash('没有选择文件', 'error')
        return redirect(url_for('index'))
    
    if file and allowed_file(file.filename):
        # 保存上传的文件
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        try:
            # 获取工作表信息
            worksheets_info, error = get_worksheet_info(file_path)
            
            if error:
                # 清理临时文件
                os.remove(file_path)
                flash(f'文件处理失败: {error}', 'error')
                return redirect(url_for('index'))
            
            # 渲染工作表选择页面
            return render_template('sheet_selection.html', 
                                 worksheets=worksheets_info,
                                 filename=filename,
                                 original_filename=file.filename)
                
        except Exception as e:
            # 清理临时文件
            if os.path.exists(file_path):
                os.remove(file_path)
            flash(f'处理过程中发生错误: {str(e)}', 'error')
            return redirect(url_for('index'))
    
    else:
        flash('请上传有效的Excel文件（.xlsx 或 .xls）', 'error')
        return redirect(url_for('index'))


@app.route('/process_sheets', methods=['POST'])
def process_sheets():
    """处理用户选择的工作表并导入数据"""
    if 'selected_sheets' not in request.form:
        flash('请选择至少一个工作表', 'error')
        return redirect(url_for('index'))
    
    if 'filename' not in request.form:
        flash('文件信息丢失，请重新上传', 'error')
        return redirect(url_for('index'))
    
    filename = request.form['filename']
    selected_sheets = request.form.getlist('selected_sheets')
    original_filename = request.form.get('original_filename', '未知文件')
    
    if not selected_sheets:
        flash('请选择至少一个工作表', 'error')
        return redirect(url_for('index'))
    
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        flash('临时文件不存在，请重新上传', 'error')
        return redirect(url_for('index'))
    
    try:
        # 处理选中的工作表
        df, error = clean_and_extract_selected_data(file_path, selected_sheets)
        
        if error:
            flash(f'数据处理失败: {error}', 'error')
            return redirect(url_for('index'))
        
        # 保存到数据库
        result = save_to_database(df)
        
        # 删除临时文件
        os.remove(file_path)
        
        if result['success']:
            flash(result['message'], 'success')
            return render_template('result.html', 
                                 result=result, 
                                 data_preview=df.head(10).to_dict('records'),
                                 selected_sheets=selected_sheets,
                                 original_filename=original_filename)
        else:
            flash(result['error'], 'error')
            return redirect(url_for('index'))
            
    except Exception as e:
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)
        flash(f'处理过程中发生错误: {str(e)}', 'error')
        return redirect(url_for('index'))


@app.route('/api/stats')
def api_stats():
    """API接口 - 获取数据库统计信息"""
    stats = get_database_stats()
    if stats:
        return jsonify(stats)
    else:
        return jsonify({'error': '暂无数据'}), 404


@app.route('/records')
def view_records():
    """查看所有记录"""
    try:
        conn = sqlite3.connect("database/motosim.db")
        cursor = conn.cursor()
        
        # 获取所有记录
        cursor.execute("""
            SELECT id, 序号, 驾校, 教练, 学员, 车型, 模拟科目, 
                   工作表, 模拟日期, 时段, 创建时间 
            FROM motosim_records 
            ORDER BY 创建时间 DESC
        """)
        records = cursor.fetchall()
        
        # 获取列名
        columns = [description[0] for description in cursor.description]
        
        # 获取去重的筛选选项（日期倒序排列）
        cursor.execute("SELECT DISTINCT 模拟日期 FROM motosim_records WHERE 模拟日期 IS NOT NULL")
        dates_raw = [row[0] for row in cursor.fetchall()]
        
        # 智能日期排序（倒序）
        def sort_date_key(date_str):
            """将日期字符串转换为可排序的格式"""
            try:
                if '/' in date_str:
                    parts = date_str.split('/')
                    if len(parts) == 2:
                        month, day = int(parts[0]), int(parts[1])
                        return (month, day)
                return (0, 0)  # 无效日期排在最后
            except:
                return (0, 0)
        
        # 按日期倒序排列（最新的在前）
        unique_dates = sorted(dates_raw, key=sort_date_key, reverse=True)
        
        cursor.execute("SELECT DISTINCT 教练 FROM motosim_records ORDER BY 教练")
        unique_teachers = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        
        # 转换为字典列表
        records_dict = []
        for record in records:
            record_dict = dict(zip(columns, record))
            records_dict.append(record_dict)
        
        return render_template('records.html', 
                             records=records_dict,
                             unique_dates=unique_dates,
                             unique_teachers=unique_teachers)
        
    except Exception as e:
        flash(f'查询记录失败: {str(e)}', 'error')
        return redirect(url_for('index'))


if __name__ == '__main__':
    # 确保必要的目录存在
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('database', exist_ok=True)
    
    # 启动应用
    app.run(debug=True, host='0.0.0.0', port=5001) 