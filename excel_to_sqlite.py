#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel到SQLite数据库转换器
将7.xlsx文件中的摩托车科二、三模拟登记表转换为SQLite数据库
"""

import pandas as pd
import sqlite3
import os
import re
from datetime import datetime


def clean_and_extract_data(excel_file):
    """清理和提取Excel数据"""
    print("正在读取和清理Excel数据...")
    
    all_data = []
    excel_data = pd.ExcelFile(excel_file)
    
    for sheet_name in excel_data.sheet_names:
        print(f"处理工作表: {sheet_name}")
        
        # 读取工作表
        df = pd.read_excel(excel_file, sheet_name=sheet_name)
        
        # 从第一行提取日期和时段信息
        raw_date_info = str(df.iloc[0, 0]) if not pd.isna(df.iloc[0, 0]) else sheet_name
        
        # 使用正则表达式解析日期和时段
        import re
        date_pattern = r'模拟日期(\d+/\d+)(上午|下午)'
        match = re.search(date_pattern, raw_date_info)
        
        if match:
            simulation_date = match.group(1)  # 提取日期部分，如"8/1"
            time_period = match.group(2)     # 提取时段部分，如"上午"
        else:
            # 如果正则匹配失败，使用默认值
            simulation_date = sheet_name
            time_period = "未知时段"
        
        print(f"  解析结果 - 模拟日期: {simulation_date}, 时段: {time_period}")
        
        # 设置正确的列名（从第二行）
        df.columns = ['序号', '驾校', '教练', '学员', '车型', '模拟科目']
        
        # 删除前两行（日期行和标题行）
        df = df.iloc[2:].reset_index(drop=True)
        
        # 过滤掉空行
        df = df.dropna(subset=['序号']).reset_index(drop=True)
        
        # 添加源信息列
        df['工作表'] = sheet_name
        df['模拟日期'] = simulation_date
        df['时段'] = time_period
        
        # 数据类型转换和清理
        df['序号'] = pd.to_numeric(df['序号'], errors='coerce')
        df = df.dropna(subset=['序号']).reset_index(drop=True)
        
        # 添加到总数据列表
        all_data.append(df)
        print(f"  提取了 {len(df)} 条记录")
    
    # 合并所有数据
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"总共合并了 {len(combined_df)} 条记录")
        return combined_df
    else:
        print("没有找到有效数据")
        return None


def create_sqlite_database(df, db_path):
    """创建SQLite数据库并插入数据"""
    print(f"正在创建SQLite数据库: {db_path}")
    
    # 确保database目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # 连接到SQLite数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 删除已存在的表（如果存在）
        cursor.execute("DROP TABLE IF EXISTS motosim_records")
        
        # 创建表结构
        create_table_sql = """
        CREATE TABLE motosim_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            序号 INTEGER,
            驾校 TEXT,
            教练 TEXT,
            学员 TEXT,
            车型 TEXT,
            模拟科目 TEXT,
            工作表 TEXT,
            模拟日期 TEXT,
            时段 TEXT,
            创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(create_table_sql)
        print("数据表创建成功")
        
        # 插入数据
        for index, row in df.iterrows():
            insert_sql = """
            INSERT INTO motosim_records 
            (序号, 驾校, 教练, 学员, 车型, 模拟科目, 工作表, 模拟日期, 时段)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            cursor.execute(insert_sql, (
                row['序号'],
                row['驾校'],
                row['教练'],
                row['学员'],
                row['车型'],
                row['模拟科目'],
                row['工作表'],
                row['模拟日期'],
                row['时段']
            ))
        
        # 提交事务
        conn.commit()
        print(f"成功插入 {len(df)} 条记录到数据库")
        
        # 验证数据
        cursor.execute("SELECT COUNT(*) FROM motosim_records")
        count = cursor.fetchone()[0]
        print(f"数据库中共有 {count} 条记录")
        
        # 显示表结构
        cursor.execute("PRAGMA table_info(motosim_records)")
        columns = cursor.fetchall()
        print("\n数据表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 显示前几条记录作为验证
        cursor.execute("SELECT * FROM motosim_records LIMIT 5")
        sample_records = cursor.fetchall()
        print(f"\n前5条记录样例:")
        for i, record in enumerate(sample_records, 1):
            print(f"  记录{i}: ID={record[0]}, 序号={record[1]}, 驾校={record[2]}, 教练={record[3]}, 学员={record[4]}")
        
        return True
        
    except Exception as e:
        print(f"创建数据库时出错: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()


def generate_summary_report(db_path):
    """生成数据统计报告"""
    print("\n生成数据统计报告...")
    
    conn = sqlite3.connect(db_path)
    
    try:
        # 基本统计
        print("=" * 60)
        print("数据库统计报告")
        print("=" * 60)
        
        # 总记录数
        total_records = pd.read_sql_query("SELECT COUNT(*) as count FROM motosim_records", conn).iloc[0]['count']
        print(f"总记录数: {total_records}")
        
        # 按工作表统计
        sheet_stats = pd.read_sql_query("SELECT 工作表, COUNT(*) as count FROM motosim_records GROUP BY 工作表", conn)
        print("\n按工作表统计:")
        for _, row in sheet_stats.iterrows():
            print(f"  {row['工作表']}: {row['count']} 条记录")
        
        # 按驾校统计
        school_stats = pd.read_sql_query("SELECT 驾校, COUNT(*) as count FROM motosim_records GROUP BY 驾校", conn)
        print("\n按驾校统计:")
        for _, row in school_stats.iterrows():
            print(f"  {row['驾校']}: {row['count']} 条记录")
        
        # 按教练统计
        teacher_stats = pd.read_sql_query("SELECT 教练, COUNT(*) as count FROM motosim_records GROUP BY 教练", conn)
        print("\n按教练统计:")
        for _, row in teacher_stats.iterrows():
            print(f"  {row['教练']}: {row['count']} 条记录")
        
        # 按车型统计
        car_type_stats = pd.read_sql_query("SELECT 车型, COUNT(*) as count FROM motosim_records GROUP BY 车型", conn)
        print("\n按车型统计:")
        for _, row in car_type_stats.iterrows():
            print(f"  {row['车型']}: {row['count']} 条记录")
        
        # 按时段统计
        time_period_stats = pd.read_sql_query("SELECT 时段, COUNT(*) as count FROM motosim_records GROUP BY 时段", conn)
        print("\n按时段统计:")
        for _, row in time_period_stats.iterrows():
            print(f"  {row['时段']}: {row['count']} 条记录")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"生成报告时出错: {e}")
    
    finally:
        conn.close()


def main():
    """主函数"""
    excel_file = "7.xlsx"
    db_path = "database/motosim.db"
    
    print("摩托车模拟登记表 Excel 到 SQLite 转换器")
    print("=" * 60)
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件 {excel_file} 不存在")
        return
    
    # 步骤1: 清理和提取数据
    df = clean_and_extract_data(excel_file)
    if df is None:
        print("数据提取失败，程序终止")
        return
    
    # 显示数据概览
    print(f"\n清理后的数据概览:")
    print(f"总记录数: {len(df)}")
    print(f"列名: {list(df.columns)}")
    print(f"前3行数据:")
    print(df.head(3))
    
    # 步骤2: 创建SQLite数据库
    success = create_sqlite_database(df, db_path)
    if not success:
        print("数据库创建失败，程序终止")
        return
    
    # 步骤3: 生成统计报告
    generate_summary_report(db_path)
    
    print(f"\n✅ 转换完成！SQLite数据库已保存到: {db_path}")
    print(f"数据库包含表: motosim_records")
    print(f"总记录数: {len(df)}")


if __name__ == "__main__":
    main() 