// 摩托车模拟登记表管理系统 - 主要JavaScript功能

// 全局变量
const MotoSim = {
    version: '1.0.0',
    config: {
        apiBaseUrl: '/api',
        uploadMaxSize: 16 * 1024 * 1024, // 16MB
        supportedFormats: ['xlsx', 'xls'],
        animationDuration: 300
    }
};

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('摩托车模拟登记表管理系统已加载', MotoSim.version);
    
    // 初始化所有功能
    initializeApp();
    initializeAnimations();
    initializeTooltips();
    initializeFileUpload();
    initializeNotifications();
});

// 初始化应用
function initializeApp() {
    // 添加页面加载动画
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
    
    // 初始化导航高亮
    highlightActiveNavigation();
    
    // 初始化响应式处理
    handleResponsive();
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResponsive);
}

// 初始化动画效果
function initializeAnimations() {
    // 为卡片添加进入动画
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
    
    // 为按钮添加点击动画
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // 创建波纹效果
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// 初始化工具提示
function initializeTooltips() {
    // 如果Bootstrap可用，初始化tooltips
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

// 初始化文件上传功能
function initializeFileUpload() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        const uploadArea = input.closest('.card-body');
        
        if (uploadArea) {
            // 拖拽上传功能
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
            });
            
            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, unhighlight, false);
            });
            
            uploadArea.addEventListener('drop', handleDrop, false);
        }
        
        // 文件大小验证
        input.addEventListener('change', function() {
            validateFileSize(this);
            validateFileType(this);
        });
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight(e) {
        e.currentTarget.classList.add('dragover');
    }
    
    function unhighlight(e) {
        e.currentTarget.classList.remove('dragover');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        const fileInput = e.currentTarget.querySelector('input[type="file"]');
        
        if (fileInput && files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }
}

// 文件大小验证
function validateFileSize(input) {
    const file = input.files[0];
    if (file && file.size > MotoSim.config.uploadMaxSize) {
        showNotification('文件大小超过限制（最大16MB）', 'error');
        input.value = '';
        return false;
    }
    return true;
}

// 文件类型验证
function validateFileType(input) {
    const file = input.files[0];
    if (file) {
        const extension = file.name.split('.').pop().toLowerCase();
        if (!MotoSim.config.supportedFormats.includes(extension)) {
            showNotification('不支持的文件格式，请上传 .xlsx 或 .xls 文件', 'error');
            input.value = '';
            return false;
        }
    }
    return true;
}

// 初始化通知系统
function initializeNotifications() {
    // 自动隐藏成功消息
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('alert-success')) {
            setTimeout(() => {
                fadeOut(alert);
            }, 5000);
        }
    });
}

// 显示通知
function showNotification(message, type = 'info', duration = 3000) {
    const alertTypes = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const icons = {
        'success': 'bi-check-circle-fill',
        'error': 'bi-exclamation-triangle-fill',
        'warning': 'bi-exclamation-triangle-fill',
        'info': 'bi-info-circle-fill'
    };
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertTypes[type]} alert-dismissible fade show`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';
    
    alert.innerHTML = `
        <i class="bi ${icons[type]} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    // 自动移除
    setTimeout(() => {
        fadeOut(alert);
    }, duration);
}

// 淡出效果
function fadeOut(element) {
    element.style.transition = 'opacity 0.3s ease';
    element.style.opacity = '0';
    setTimeout(() => {
        if (element.parentNode) {
            element.parentNode.removeChild(element);
        }
    }, 300);
}

// 高亮当前导航
function highlightActiveNavigation() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPath || (currentPath === '/' && href === '/')) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// 响应式处理
function handleResponsive() {
    const isMobile = window.innerWidth < 768;
    const tables = document.querySelectorAll('.table-responsive');
    
    if (isMobile) {
        // 移动端优化
        tables.forEach(table => {
            table.style.fontSize = '0.875rem';
        });
    } else {
        // 桌面端恢复
        tables.forEach(table => {
            table.style.fontSize = '';
        });
    }
}

// API调用函数
const API = {
    // 获取统计信息
    async getStats() {
        try {
            const response = await fetch(MotoSim.config.apiBaseUrl + '/stats');
            if (response.ok) {
                return await response.json();
            }
            throw new Error('获取统计信息失败');
        } catch (error) {
            console.error('API错误:', error);
            showNotification('获取统计信息失败', 'error');
            return null;
        }
    },
    
    // 刷新统计信息
    async refreshStats() {
        const stats = await this.getStats();
        if (stats) {
            updateStatsDisplay(stats);
        }
    }
};

// 更新统计显示
function updateStatsDisplay(stats) {
    // 更新总记录数
    const totalElement = document.getElementById('totalRecords');
    if (totalElement) {
        totalElement.textContent = stats.total_count || 0;
    }
    
    // 可以根据需要添加更多统计信息的更新
}

// 数据导出功能
function exportToCSV(data, filename) {
    const csvContent = "data:text/csv;charset=utf-8," + data;
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化数字
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('已复制到剪贴板', 'success');
        });
    } else {
        // 兼容较旧的浏览器
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            showNotification('已复制到剪贴板', 'success');
        } catch (err) {
            showNotification('复制失败', 'error');
        }
        document.body.removeChild(textArea);
    }
}

// 页面加载进度条
function showLoadingBar() {
    const loadingBar = document.createElement('div');
    loadingBar.id = 'loadingBar';
    loadingBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, #007bff, #0056b3);
        z-index: 10000;
        transition: width 0.3s ease;
    `;
    document.body.appendChild(loadingBar);
    
    let width = 0;
    const interval = setInterval(() => {
        width += Math.random() * 30;
        if (width > 90) width = 90;
        loadingBar.style.width = width + '%';
    }, 100);
    
    return {
        complete: () => {
            clearInterval(interval);
            loadingBar.style.width = '100%';
            setTimeout(() => {
                loadingBar.remove();
            }, 300);
        }
    };
}

// 滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 添加CSS动画样式
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .dragover {
        background-color: rgba(0, 123, 255, 0.1) !important;
        border-color: #007bff !important;
    }
    
    .nav-link.active {
        background-color: rgba(255, 255, 255, 0.2) !important;
        border-radius: 0.375rem;
    }
`;
document.head.appendChild(style);

// 工作表选择相关功能
const WorksheetSelection = {
    // 初始化工作表选择功能
    init: function() {
        if (!document.querySelector('.worksheet-checkbox')) return;
        
        this.bindEvents();
        this.updateSelectionState();
        this.initializeSmartSelection();
    },
    
    // 绑定事件
    bindEvents: function() {
        const checkboxes = document.querySelectorAll('.worksheet-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => this.updateSelectionState());
        });
    },
    
    // 更新选择状态
    updateSelectionState: function() {
        const checkboxes = document.querySelectorAll('.worksheet-checkbox');
        const selectedCount = document.getElementById('selectedCount');
        const submitBtn = document.getElementById('submitBtn');
        const submitBtnBottom = document.getElementById('submitBtnBottom');
        const worksheetCards = document.querySelectorAll('.worksheet-card');
        
        const checkedCount = document.querySelectorAll('.worksheet-checkbox:checked').length;
        
        if (selectedCount) selectedCount.textContent = checkedCount;
        
        const hasSelection = checkedCount > 0;
        if (submitBtn) submitBtn.disabled = !hasSelection;
        if (submitBtnBottom) submitBtnBottom.disabled = !hasSelection;
        
        // 更新卡片样式
        checkboxes.forEach((checkbox, index) => {
            const card = worksheetCards[index];
            if (card) {
                if (checkbox.checked) {
                    card.classList.add('border-primary', 'bg-light');
                } else {
                    card.classList.remove('border-primary', 'bg-light');
                }
            }
        });
    },
    
    // 智能选择推荐
    initializeSmartSelection: function() {
        const worksheets = document.querySelectorAll('.worksheet-checkbox');
        if (worksheets.length <= 2) {
            // 如果工作表数量较少，默认全选
            this.selectAll();
        }
    },
    
    // 按时段模式选择
    selectByTimePattern: function() {
        const checkboxes = document.querySelectorAll('.worksheet-checkbox');
        const timePatterns = {};
        
        checkboxes.forEach(checkbox => {
            const card = checkbox.closest('.worksheet-card');
            const timeElement = card.querySelector('.badge');
            if (timeElement) {
                const timeText = timeElement.textContent.trim();
                if (!timePatterns[timeText]) timePatterns[timeText] = [];
                timePatterns[timeText].push(checkbox);
            }
        });
        
        // 选择数量最多的时段
        let maxCount = 0;
        let selectedPattern = '';
        Object.keys(timePatterns).forEach(pattern => {
            if (timePatterns[pattern].length > maxCount) {
                maxCount = timePatterns[pattern].length;
                selectedPattern = pattern;
            }
        });
        
        if (selectedPattern) {
            this.selectNone();
            timePatterns[selectedPattern].forEach(checkbox => {
                checkbox.checked = true;
            });
            this.updateSelectionState();
            showNotification(`已选择所有"${selectedPattern}"时段的工作表`, 'success');
        }
    },
    
    // 按日期模式选择
    selectByDatePattern: function() {
        const checkboxes = document.querySelectorAll('.worksheet-checkbox');
        const datePatterns = {};
        
        checkboxes.forEach(checkbox => {
            const card = checkbox.closest('.worksheet-card');
            const dateElement = card.querySelector('.text-primary');
            if (dateElement) {
                const dateText = dateElement.textContent.trim();
                if (!datePatterns[dateText]) datePatterns[dateText] = [];
                datePatterns[dateText].push(checkbox);
            }
        });
        
        // 选择数量最多的日期
        let maxCount = 0;
        let selectedPattern = '';
        Object.keys(datePatterns).forEach(pattern => {
            if (datePatterns[pattern].length > maxCount) {
                maxCount = datePatterns[pattern].length;
                selectedPattern = pattern;
            }
        });
        
        if (selectedPattern) {
            this.selectNone();
            datePatterns[selectedPattern].forEach(checkbox => {
                checkbox.checked = true;
            });
            this.updateSelectionState();
            showNotification(`已选择所有"${selectedPattern}"日期的工作表`, 'success');
        }
    },
    
    // 全选
    selectAll: function() {
        document.querySelectorAll('.worksheet-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        this.updateSelectionState();
    },
    
    // 取消全选
    selectNone: function() {
        document.querySelectorAll('.worksheet-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        this.updateSelectionState();
    },
    
    // 反选
    selectReverse: function() {
        document.querySelectorAll('.worksheet-checkbox').forEach(checkbox => {
            checkbox.checked = !checkbox.checked;
        });
        this.updateSelectionState();
    }
};

// 扩展MotoSim对象
MotoSim.WorksheetSelection = WorksheetSelection;

// 在DOM加载完成后初始化工作表选择功能
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        WorksheetSelection.init();
    }, 100);
});

// 导出到全局作用域
window.MotoSim = MotoSim;
window.showNotification = showNotification;
window.API = API;
window.copyToClipboard = copyToClipboard;
window.scrollToTop = scrollToTop; 