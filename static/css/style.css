/* 摩托车模拟登记表管理系统 - 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --gradient-primary: linear-gradient(135deg, #007bff, #0056b3);
    --gradient-success: linear-gradient(135deg, #28a745, #1e7e34);
    --gradient-info: linear-gradient(135deg, #17a2b8, #138496);
    --gradient-warning: linear-gradient(135deg, #ffc107, #e0a800);
    
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* 页面整体样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* 导航栏增强 */
.navbar {
    box-shadow: var(--shadow);
    background: var(--gradient-primary) !important;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* 卡片样式增强 */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
}

/* 渐变背景 */
.bg-gradient {
    background: var(--gradient-primary);
}

/* 按钮样式增强 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-success {
    background: var(--gradient-success);
    border: none;
}

.btn-info {
    background: var(--gradient-info);
    border: none;
}

.btn-warning {
    background: var(--gradient-warning);
    border: none;
    color: #212529;
}

/* 表单样式增强 */
.form-control, .form-select {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group-text {
    border-radius: 0.5rem 0 0 0.5rem;
    background-color: var(--light-color);
    border: 2px solid #e9ecef;
    border-right: none;
}

/* 文件上传区域 */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.1);
}

/* 进度条样式 */
.progress {
    height: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
}

.progress-bar {
    border-radius: 0.5rem;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    width: 100%;
    text-align: center;
    line-height: 1rem;
    color: white;
    font-weight: 500;
    font-size: 0.875rem;
}

/* 表格样式增强 */
.table {
    border-radius: 0.75rem;
    overflow: hidden;
}

.table thead th {
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 徽章样式增强 */
.badge {
    font-weight: 500;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
}

/* 警告框样式增强 */
.alert {
    border: none;
    border-radius: 0.75rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(30, 126, 52, 0.1));
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(176, 42, 55, 0.1));
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(19, 132, 150, 0.1));
    color: #0c5460;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 应用动画 */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.75rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    h1, .h1 {
        font-size: 1.75rem;
    }
    
    h2, .h2 {
        font-size: 1.5rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 搜索高亮 */
mark {
    background-color: #fff3cd;
    color: #856404;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 600;
}

/* 加载状态 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: #2c3e50;
    color: white;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* 页脚样式 */
footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

/* 统计卡片特殊样式 */
.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
}

/* 图标增强 */
.bi {
    vertical-align: -0.125em;
}

/* 特殊效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.glow {
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
    /* 深色模式样式将在这里添加 */
} 